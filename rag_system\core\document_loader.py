"""
Document loader for various file formats.
"""

import logging
from pathlib import Path
from typing import List, Union, Optional
from langchain.docstore.document import Document as LangchainDocument
from langchain_community.document_loaders import (
    PyPDFLoader,
    Docx2txtLoader,
    TextLoader,
    CSVLoader,
    UnstructuredHTMLLoader,
    UnstructuredMarkdownLoader
)
import pandas as pd

logger = logging.getLogger(__name__)


class DocumentLoader:
    """
    A flexible document loader that can handle multiple file formats.
    """
    
    SUPPORTED_EXTENSIONS = {
        '.pdf': PyPDFLoader,
        '.txt': TextLoader,
        '.md': UnstructuredMarkdownLoader,
        '.docx': Docx2txtLoader,
        '.csv': CSVLoader,
        '.html': UnstructuredHTMLLoader,
        '.htm': UnstructuredHTMLLoader,
    }
    
    def __init__(self):
        """Initialize the document loader."""
        self.documents = []
    
    def load_file(self, file_path: Union[str, Path]) -> List[LangchainDocument]:
        """
        Load a single file and return documents.
        
        Args:
            file_path: Path to the file to load
            
        Returns:
            List of LangchainDocument objects
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        extension = file_path.suffix.lower()
        
        if extension not in self.SUPPORTED_EXTENSIONS:
            logger.warning(f"Unsupported file extension: {extension}. Treating as text file.")
            loader_class = TextLoader
        else:
            loader_class = self.SUPPORTED_EXTENSIONS[extension]
        
        try:
            loader = loader_class(str(file_path))
            documents = loader.load()
            
            # Add metadata
            for doc in documents:
                doc.metadata.update({
                    'source': str(file_path),
                    'file_type': extension,
                    'file_name': file_path.name
                })
            
            logger.info(f"Loaded {len(documents)} documents from {file_path}")
            return documents
            
        except Exception as e:
            logger.error(f"Error loading file {file_path}: {e}")
            raise
    
    def load_directory(self, directory_path: Union[str, Path], 
                      recursive: bool = True,
                      file_extensions: Optional[List[str]] = None) -> List[LangchainDocument]:
        """
        Load all supported files from a directory.
        
        Args:
            directory_path: Path to the directory
            recursive: Whether to search subdirectories
            file_extensions: List of file extensions to include (if None, use all supported)
            
        Returns:
            List of LangchainDocument objects
        """
        directory_path = Path(directory_path)
        
        if not directory_path.exists():
            raise FileNotFoundError(f"Directory not found: {directory_path}")
        
        if not directory_path.is_dir():
            raise ValueError(f"Path is not a directory: {directory_path}")
        
        if file_extensions is None:
            file_extensions = list(self.SUPPORTED_EXTENSIONS.keys())
        
        # Normalize extensions
        file_extensions = [ext.lower() if ext.startswith('.') else f'.{ext.lower()}' 
                          for ext in file_extensions]
        
        all_documents = []
        
        # Find files
        if recursive:
            pattern = "**/*"
        else:
            pattern = "*"
        
        for file_path in directory_path.glob(pattern):
            if file_path.is_file() and file_path.suffix.lower() in file_extensions:
                try:
                    documents = self.load_file(file_path)
                    all_documents.extend(documents)
                except Exception as e:
                    logger.error(f"Failed to load {file_path}: {e}")
                    continue
        
        logger.info(f"Loaded {len(all_documents)} documents from {directory_path}")
        return all_documents
    
    def load_from_urls(self, urls: List[str]) -> List[LangchainDocument]:
        """
        Load documents from URLs (basic implementation).
        
        Args:
            urls: List of URLs to load
            
        Returns:
            List of LangchainDocument objects
        """
        try:
            from langchain_community.document_loaders import WebBaseLoader
        except ImportError:
            raise ImportError("WebBaseLoader not available. Install with: pip install langchain-community[web]")
        
        all_documents = []
        
        for url in urls:
            try:
                loader = WebBaseLoader(url)
                documents = loader.load()
                
                for doc in documents:
                    doc.metadata.update({
                        'source': url,
                        'file_type': 'web',
                        'url': url
                    })
                
                all_documents.extend(documents)
                logger.info(f"Loaded {len(documents)} documents from {url}")
                
            except Exception as e:
                logger.error(f"Failed to load URL {url}: {e}")
                continue
        
        return all_documents
    
    def load_from_dataset(self, dataset_name: str, split: str = "train", 
                         text_column: str = "text") -> List[LangchainDocument]:
        """
        Load documents from a HuggingFace dataset.
        
        Args:
            dataset_name: Name of the dataset
            split: Dataset split to load
            text_column: Name of the text column
            
        Returns:
            List of LangchainDocument objects
        """
        try:
            from datasets import load_dataset
        except ImportError:
            raise ImportError("datasets not available. Install with: pip install datasets")
        
        try:
            dataset = load_dataset(dataset_name, split=split)
            documents = []
            
            for i, item in enumerate(dataset):
                if text_column in item:
                    doc = LangchainDocument(
                        page_content=item[text_column],
                        metadata={
                            'source': f"{dataset_name}:{split}:{i}",
                            'dataset': dataset_name,
                            'split': split,
                            'index': i,
                            **{k: v for k, v in item.items() if k != text_column}
                        }
                    )
                    documents.append(doc)
            
            logger.info(f"Loaded {len(documents)} documents from dataset {dataset_name}")
            return documents
            
        except Exception as e:
            logger.error(f"Failed to load dataset {dataset_name}: {e}")
            raise
    
    def get_supported_extensions(self) -> List[str]:
        """Get list of supported file extensions."""
        return list(self.SUPPORTED_EXTENSIONS.keys())
