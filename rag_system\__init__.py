"""
Advanced RAG System with LangChain
==================================

A modular, user-friendly RAG (Retrieval Augmented Generation) system 
based on the HuggingFace cookbook implementation.

Features:
- Modular architecture for easy customization
- CUDA support for GPU acceleration
- Multiple vector database backends
- Document reranking capabilities
- User-friendly CLI and web interfaces
- Comprehensive configuration management

Author: RAG System Team
License: MIT
"""

__version__ = "1.0.0"
__author__ = "RAG System Team"

from .core import (
    DocumentLoader,
    TextSplitter,
    EmbeddingModel,
    VectorStore,
    Retriever,
    Reranker,
    LLMWrapper,
    RAGPipeline
)

from .config import RAGConfig

__all__ = [
    "DocumentLoader",
    "TextSplitter", 
    "EmbeddingModel",
    "VectorStore",
    "Retriever",
    "Reranker",
    "LLMWrapper",
    "RAGPipeline",
    "RAGConfig"
]
