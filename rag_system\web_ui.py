"""
Web interface for the RAG system using Gradio.
"""

import gradio as gr
import logging
from pathlib import Path
from typing import Op<PERSON>, <PERSON><PERSON>, List
import json

from .config import RAGConfig
from .core.pipeline import RAGPipeline

logger = logging.getLogger(__name__)


class RAGWebUI:
    """Web interface for the RAG system."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the web UI.
        
        Args:
            config_path: Path to configuration file
        """
        # Load configuration
        if config_path:
            self.config = RAGConfig.from_file(config_path)
        else:
            self.config = RAGConfig.from_env()
        
        self.pipeline = None
        self.is_initialized = False
        self.documents_loaded = False
        
    def initialize_pipeline(self) -> str:
        """Initialize the RAG pipeline."""
        try:
            if self.pipeline is None:
                self.pipeline = RAGPipeline(self.config)
            
            if not self.is_initialized:
                self.pipeline.initialize()
                self.is_initialized = True
                
                # Try to load existing pipeline
                if (self.config.vector_store.persist_directory and 
                    Path(self.config.vector_store.persist_directory).exists()):
                    try:
                        self.pipeline.load_pipeline()
                        self.documents_loaded = True
                        return "✅ Pipeline initialized and existing documents loaded!"
                    except Exception as e:
                        logger.warning(f"Could not load existing pipeline: {e}")
                
                return "✅ Pipeline initialized! Please load documents to start querying."
            
            return "✅ Pipeline already initialized."
            
        except Exception as e:
            logger.error(f"Failed to initialize pipeline: {e}")
            return f"❌ Error initializing pipeline: {str(e)}"
    
    def load_documents(self, file_paths: List[str], source_type: str) -> str:
        """
        Load documents into the pipeline.
        
        Args:
            file_paths: List of file paths
            source_type: Type of source
            
        Returns:
            Status message
        """
        if not self.is_initialized:
            return "❌ Please initialize the pipeline first."
        
        if not file_paths:
            return "❌ Please select files to upload."
        
        try:
            total_chunks = 0
            for file_path in file_paths:
                chunks = self.pipeline.load_documents(file_path, source_type)
                total_chunks += chunks
            
            self.documents_loaded = True
            
            # Save pipeline
            self.pipeline.save_pipeline()
            
            return f"✅ Successfully loaded {total_chunks} document chunks from {len(file_paths)} files!"
            
        except Exception as e:
            logger.error(f"Failed to load documents: {e}")
            return f"❌ Error loading documents: {str(e)}"
    
    def query_pipeline(self, question: str, use_reranker: bool, top_k: int) -> Tuple[str, str, str]:
        """
        Query the RAG pipeline.
        
        Args:
            question: User question
            use_reranker: Whether to use reranker
            top_k: Number of documents to retrieve
            
        Returns:
            Tuple of (answer, sources, statistics)
        """
        if not self.is_initialized:
            return "❌ Please initialize the pipeline first.", "", ""
        
        if not self.documents_loaded:
            return "❌ Please load documents first.", "", ""
        
        if not question.strip():
            return "❌ Please enter a question.", "", ""
        
        try:
            result = self.pipeline.query(
                question=question,
                top_k=top_k,
                use_reranker=use_reranker
            )
            
            # Format sources
            sources_text = "📚 **Sources:**\n\n"
            for i, doc in enumerate(result['source_documents']):
                source = doc.metadata.get('source', 'Unknown')
                content_preview = doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content
                sources_text += f"**{i+1}. {source}**\n{content_preview}\n\n"
            
            # Format statistics
            stats_text = f"""📊 **Query Statistics:**
            
- **Documents Retrieved:** {result['retrieval_count']}
- **Documents Used:** {result['final_count']}
- **Reranked:** {'Yes' if result['reranked'] else 'No'}
- **Context Length:** {result['context_length']} characters
- **Prompt Length:** {result['prompt_length']} tokens
"""
            
            return result['answer'], sources_text, stats_text
            
        except Exception as e:
            logger.error(f"Query failed: {e}")
            return f"❌ Error processing query: {str(e)}", "", ""
    
    def get_pipeline_info(self) -> str:
        """Get pipeline information."""
        if not self.is_initialized:
            return "Pipeline not initialized."
        
        try:
            stats = self.pipeline.get_pipeline_stats()
            
            info_text = f"""🔧 **Pipeline Configuration:**

**Models:**
- Embedding: {stats['config']['embedding_model']}
- LLM: {stats['config']['llm_model']}
- Vector Store: {stats['config']['vector_store_backend']}
- Reranker: {'Enabled' if stats['config']['reranker_enabled'] else 'Disabled'}

**Settings:**
- Chunk Size: {stats['config']['chunk_size']} tokens
- Documents Loaded: {'Yes' if self.documents_loaded else 'No'}
"""
            
            if 'vector_store_stats' in stats:
                info_text += f"- Document Count: {stats['vector_store_stats']['document_count']}\n"
            
            if 'embedding_info' in stats:
                emb_info = stats['embedding_info']
                info_text += f"- Embedding Dimension: {emb_info['embedding_dimension']}\n"
                info_text += f"- Device: {emb_info['device']}\n"
            
            return info_text
            
        except Exception as e:
            return f"Error getting pipeline info: {str(e)}"
    
    def create_interface(self) -> gr.Blocks:
        """Create the Gradio interface."""
        
        with gr.Blocks(title="Advanced RAG System", theme=gr.themes.Soft()) as interface:
            gr.Markdown("# 🤖 Advanced RAG System")
            gr.Markdown("Retrieval Augmented Generation with LangChain and HuggingFace")
            
            with gr.Tab("💬 Chat"):
                with gr.Row():
                    with gr.Column(scale=2):
                        question_input = gr.Textbox(
                            label="Your Question",
                            placeholder="Ask anything about your documents...",
                            lines=3
                        )
                        
                        with gr.Row():
                            use_reranker = gr.Checkbox(
                                label="Use Reranker",
                                value=True,
                                info="Improve answer quality with document reranking"
                            )
                            top_k = gr.Slider(
                                minimum=1,
                                maximum=20,
                                value=5,
                                step=1,
                                label="Documents to Retrieve",
                                info="Number of documents to consider"
                            )
                        
                        query_btn = gr.Button("🔍 Ask Question", variant="primary")
                    
                    with gr.Column(scale=1):
                        pipeline_info = gr.Markdown(
                            value="Click 'Initialize Pipeline' to start",
                            label="Pipeline Status"
                        )
                        
                        init_btn = gr.Button("🚀 Initialize Pipeline")
                        refresh_info_btn = gr.Button("🔄 Refresh Info")
                
                answer_output = gr.Textbox(
                    label="Answer",
                    lines=5,
                    interactive=False
                )
                
                with gr.Row():
                    sources_output = gr.Markdown(
                        label="Sources",
                        value="Sources will appear here after querying"
                    )
                    stats_output = gr.Markdown(
                        label="Statistics",
                        value="Query statistics will appear here"
                    )
            
            with gr.Tab("📁 Document Management"):
                gr.Markdown("## Upload Documents")
                
                file_upload = gr.File(
                    label="Select Documents",
                    file_count="multiple",
                    file_types=[".txt", ".pdf", ".docx", ".md", ".csv"]
                )
                
                source_type = gr.Dropdown(
                    choices=["auto", "file", "directory"],
                    value="auto",
                    label="Source Type",
                    info="How to process the uploaded files"
                )
                
                load_btn = gr.Button("📤 Load Documents", variant="primary")
                load_status = gr.Textbox(
                    label="Status",
                    value="Ready to load documents",
                    interactive=False
                )
            
            with gr.Tab("⚙️ Settings"):
                gr.Markdown("## Configuration")
                gr.Markdown("Current pipeline configuration:")
                
                config_display = gr.JSON(
                    value=self.config.model_dump(),
                    label="Configuration"
                )
            
            # Event handlers
            init_btn.click(
                fn=self.initialize_pipeline,
                outputs=[pipeline_info]
            )
            
            refresh_info_btn.click(
                fn=self.get_pipeline_info,
                outputs=[pipeline_info]
            )
            
            load_btn.click(
                fn=self.load_documents,
                inputs=[file_upload, source_type],
                outputs=[load_status]
            )
            
            query_btn.click(
                fn=self.query_pipeline,
                inputs=[question_input, use_reranker, top_k],
                outputs=[answer_output, sources_output, stats_output]
            )
            
            # Auto-refresh pipeline info on load
            interface.load(
                fn=self.get_pipeline_info,
                outputs=[pipeline_info]
            )
        
        return interface
    
    def launch(self, **kwargs):
        """Launch the web interface."""
        interface = self.create_interface()
        interface.launch(**kwargs)


def create_web_ui(config_path: Optional[str] = None, **launch_kwargs):
    """
    Create and launch the web UI.
    
    Args:
        config_path: Path to configuration file
        **launch_kwargs: Arguments to pass to Gradio launch
    """
    ui = RAGWebUI(config_path)
    ui.launch(**launch_kwargs)


if __name__ == "__main__":
    create_web_ui(share=False, server_name="0.0.0.0", server_port=7860)
