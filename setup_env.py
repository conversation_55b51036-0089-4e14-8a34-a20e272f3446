#!/usr/bin/env python3
"""
Setup script for RAG system environment.
This script helps set up the Python virtual environment and install dependencies.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during {description}:")
        print(f"Command: {command}")
        print(f"Error: {e.stderr}")
        return None

def check_cuda():
    """Check if CUDA is available."""
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA is available! Device count: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
            return True
        else:
            print("⚠️  CUDA is not available. Using CPU mode.")
            return False
    except ImportError:
        print("⚠️  PyTorch not installed yet. Will check CUDA after installation.")
        return False

def setup_environment():
    """Set up the Python environment."""
    print("🚀 Setting up RAG System Environment")
    print("=" * 50)
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 3.8 or higher is required!")
        sys.exit(1)
    
    print(f"✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Create virtual environment if it doesn't exist
    venv_path = Path(".venv")
    if not venv_path.exists():
        run_command("python -m venv .venv", "Creating virtual environment")
    else:
        print("✅ Virtual environment already exists")
    
    # Determine activation command based on OS
    if os.name == 'nt':  # Windows
        activate_cmd = ".venv\\Scripts\\activate"
        pip_cmd = ".venv\\Scripts\\pip"
    else:  # Unix/Linux/MacOS
        activate_cmd = "source .venv/bin/activate"
        pip_cmd = ".venv/bin/pip"
    
    print(f"\n📝 To activate the virtual environment, run:")
    print(f"   {activate_cmd}")
    
    # Upgrade pip
    run_command(f"{pip_cmd} install --upgrade pip", "Upgrading pip")
    
    # Install requirements
    if Path("requirements.txt").exists():
        run_command(f"{pip_cmd} install -r requirements.txt", "Installing requirements")
    else:
        print("⚠️  requirements.txt not found. Please create it first.")
    
    # Check CUDA after installation
    print("\n🔍 Checking CUDA availability...")
    try:
        # Try to import torch to check CUDA
        result = subprocess.run([pip_cmd.replace("pip", "python"), "-c", 
                               "import torch; print('CUDA available:', torch.cuda.is_available())"],
                              capture_output=True, text=True)
        print(result.stdout.strip())
    except Exception as e:
        print(f"Could not check CUDA: {e}")
    
    print("\n🎉 Environment setup complete!")
    print("\nNext steps:")
    print("1. Activate the virtual environment:")
    print(f"   {activate_cmd}")
    print("2. Run the RAG system:")
    print("   python -m rag_system.cli --help")

if __name__ == "__main__":
    setup_environment()
