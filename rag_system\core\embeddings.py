"""
Embedding model wrapper for document and query embeddings.
"""

import logging
import torch
from typing import List, Optional
from sentence_transformers import SentenceTransformer
from langchain.embeddings.base import Embeddings
from ..config import EmbeddingConfig

logger = logging.getLogger(__name__)


class EmbeddingModel(Embeddings):
    """
    Wrapper for sentence transformer embedding models with CUDA support.
    """
    
    def __init__(self, config: EmbeddingConfig):
        """
        Initialize the embedding model.
        
        Args:
            config: Embedding configuration
        """
        self.config = config
        self.device = self._determine_device()
        
        logger.info(f"Loading embedding model: {config.model_name}")
        logger.info(f"Using device: {self.device}")
        
        try:
            self.model = SentenceTransformer(config.model_name, device=self.device)
            
            # Set max sequence length if specified
            if hasattr(self.model, 'max_seq_length'):
                if config.max_seq_length != self.model.max_seq_length:
                    logger.info(f"Setting max_seq_length from {self.model.max_seq_length} to {config.max_seq_length}")
                    self.model.max_seq_length = config.max_seq_length
            
            logger.info(f"Successfully loaded embedding model")
            logger.info(f"Model max sequence length: {getattr(self.model, 'max_seq_length', 'unknown')}")
            logger.info(f"Embedding dimension: {self.get_embedding_dimension()}")
            
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            raise
    
    def _determine_device(self) -> str:
        """Determine the best device to use."""
        if self.config.device == "auto":
            if torch.cuda.is_available():
                device = "cuda"
                logger.info(f"CUDA available with {torch.cuda.device_count()} GPU(s)")
                for i in range(torch.cuda.device_count()):
                    logger.info(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
            else:
                device = "cpu"
                logger.info("CUDA not available, using CPU")
        else:
            device = self.config.device
        
        return device
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a list of documents.
        
        Args:
            texts: List of document texts to embed
            
        Returns:
            List of embedding vectors
        """
        if not texts:
            return []
        
        try:
            # Filter out empty texts
            non_empty_texts = [text for text in texts if text.strip()]
            if len(non_empty_texts) != len(texts):
                logger.warning(f"Filtered out {len(texts) - len(non_empty_texts)} empty texts")
            
            if not non_empty_texts:
                return []
            
            # Encode in batches
            embeddings = self.model.encode(
                non_empty_texts,
                batch_size=self.config.batch_size,
                show_progress_bar=len(non_empty_texts) > 100,
                convert_to_numpy=True,
                normalize_embeddings=True
            )
            
            # Convert to list of lists
            return embeddings.tolist()
            
        except Exception as e:
            logger.error(f"Error embedding documents: {e}")
            raise
    
    def embed_query(self, text: str) -> List[float]:
        """
        Embed a single query.
        
        Args:
            text: Query text to embed
            
        Returns:
            Embedding vector
        """
        if not text.strip():
            raise ValueError("Query text cannot be empty")
        
        try:
            embedding = self.model.encode(
                [text],
                batch_size=1,
                show_progress_bar=False,
                convert_to_numpy=True,
                normalize_embeddings=True
            )
            
            return embedding[0].tolist()
            
        except Exception as e:
            logger.error(f"Error embedding query: {e}")
            raise
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of the embedding vectors."""
        try:
            # Get dimension from model
            if hasattr(self.model, 'get_sentence_embedding_dimension'):
                return self.model.get_sentence_embedding_dimension()
            elif hasattr(self.model, 'encode'):
                # Test with a dummy text
                test_embedding = self.model.encode(["test"], convert_to_numpy=True)
                return test_embedding.shape[1]
            else:
                raise AttributeError("Cannot determine embedding dimension")
        except Exception as e:
            logger.error(f"Error getting embedding dimension: {e}")
            raise
    
    def encode_batch(self, texts: List[str], batch_size: Optional[int] = None) -> List[List[float]]:
        """
        Encode a batch of texts with custom batch size.
        
        Args:
            texts: List of texts to encode
            batch_size: Custom batch size (if None, use config default)
            
        Returns:
            List of embedding vectors
        """
        if batch_size is None:
            batch_size = self.config.batch_size
        
        return self.embed_documents(texts)
    
    def similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            Cosine similarity score
        """
        try:
            import numpy as np
            
            # Convert to numpy arrays
            emb1 = np.array(embedding1)
            emb2 = np.array(embedding2)
            
            # Calculate cosine similarity
            dot_product = np.dot(emb1, emb2)
            norm1 = np.linalg.norm(emb1)
            norm2 = np.linalg.norm(emb2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
            
        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            raise
    
    def get_model_info(self) -> dict:
        """Get information about the loaded model."""
        info = {
            "model_name": self.config.model_name,
            "device": self.device,
            "max_seq_length": getattr(self.model, 'max_seq_length', 'unknown'),
            "embedding_dimension": self.get_embedding_dimension(),
            "batch_size": self.config.batch_size
        }
        
        # Add CUDA info if available
        if self.device == "cuda" and torch.cuda.is_available():
            info["cuda_device_count"] = torch.cuda.device_count()
            info["cuda_current_device"] = torch.cuda.current_device()
            info["cuda_device_name"] = torch.cuda.get_device_name()
        
        return info
