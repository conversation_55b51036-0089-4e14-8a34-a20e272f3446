"""
Text splitter for chunking documents.
"""

import logging
from typing import List, Optional
from langchain.docstore.document import Document as LangchainDocument
from langchain.text_splitter import RecursiveCharacterTextSplitter
from transformers import AutoTokenizer
from ..config import TextSplitterConfig

logger = logging.getLogger(__name__)


class TextSplitter:
    """
    Advanced text splitter with token-aware chunking.
    """
    
    def __init__(self, config: TextSplitterConfig, tokenizer_name: Optional[str] = None):
        """
        Initialize the text splitter.
        
        Args:
            config: Text splitter configuration
            tokenizer_name: Name of the tokenizer to use for token counting
        """
        self.config = config
        self.tokenizer_name = tokenizer_name
        self.tokenizer = None
        
        if tokenizer_name:
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
                logger.info(f"Loaded tokenizer: {tokenizer_name}")
            except Exception as e:
                logger.warning(f"Failed to load tokenizer {tokenizer_name}: {e}")
                self.tokenizer = None
        
        self._create_splitter()
    
    def _create_splitter(self):
        """Create the text splitter based on configuration."""
        if self.tokenizer:
            # Use token-based splitting
            self.splitter = RecursiveCharacterTextSplitter.from_huggingface_tokenizer(
                self.tokenizer,
                chunk_size=self.config.chunk_size,
                chunk_overlap=self.config.chunk_overlap,
                add_start_index=True,
                strip_whitespace=True,
                separators=self.config.separators,
            )
            logger.info(f"Created token-based text splitter with chunk size: {self.config.chunk_size}")
        else:
            # Use character-based splitting
            self.splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.config.chunk_size,
                chunk_overlap=self.config.chunk_overlap,
                add_start_index=True,
                strip_whitespace=True,
                separators=self.config.separators,
            )
            logger.info(f"Created character-based text splitter with chunk size: {self.config.chunk_size}")
    
    def split_documents(self, documents: List[LangchainDocument]) -> List[LangchainDocument]:
        """
        Split documents into chunks.
        
        Args:
            documents: List of documents to split
            
        Returns:
            List of document chunks
        """
        if not documents:
            return []
        
        logger.info(f"Splitting {len(documents)} documents...")
        
        # Split documents
        chunks = []
        for doc in documents:
            doc_chunks = self.splitter.split_documents([doc])
            chunks.extend(doc_chunks)
        
        # Remove duplicates based on content
        unique_chunks = self._remove_duplicates(chunks)
        
        logger.info(f"Created {len(unique_chunks)} unique chunks from {len(documents)} documents")
        
        # Log chunk size statistics if tokenizer is available
        if self.tokenizer:
            self._log_chunk_statistics(unique_chunks)
        
        return unique_chunks
    
    def _remove_duplicates(self, chunks: List[LangchainDocument]) -> List[LangchainDocument]:
        """Remove duplicate chunks based on content."""
        seen_content = set()
        unique_chunks = []
        
        for chunk in chunks:
            content_hash = hash(chunk.page_content)
            if content_hash not in seen_content:
                seen_content.add(content_hash)
                unique_chunks.append(chunk)
        
        removed_count = len(chunks) - len(unique_chunks)
        if removed_count > 0:
            logger.info(f"Removed {removed_count} duplicate chunks")
        
        return unique_chunks
    
    def _log_chunk_statistics(self, chunks: List[LangchainDocument]):
        """Log statistics about chunk sizes."""
        if not chunks or not self.tokenizer:
            return
        
        token_lengths = []
        for chunk in chunks:
            try:
                tokens = self.tokenizer.encode(chunk.page_content)
                token_lengths.append(len(tokens))
            except Exception:
                continue
        
        if token_lengths:
            avg_length = sum(token_lengths) / len(token_lengths)
            max_length = max(token_lengths)
            min_length = min(token_lengths)
            
            logger.info(f"Chunk statistics - Avg: {avg_length:.1f}, Min: {min_length}, Max: {max_length} tokens")
            
            # Warn if chunks are too large
            oversized_chunks = [l for l in token_lengths if l > self.config.chunk_size * 1.1]
            if oversized_chunks:
                logger.warning(f"{len(oversized_chunks)} chunks exceed target size by >10%")
    
    def split_text(self, text: str, metadata: Optional[dict] = None) -> List[LangchainDocument]:
        """
        Split a single text into chunks.
        
        Args:
            text: Text to split
            metadata: Optional metadata to add to chunks
            
        Returns:
            List of document chunks
        """
        if not text.strip():
            return []
        
        # Create a document from the text
        doc = LangchainDocument(
            page_content=text,
            metadata=metadata or {}
        )
        
        return self.split_documents([doc])
    
    def get_chunk_size_info(self) -> dict:
        """Get information about the current chunk size configuration."""
        info = {
            "chunk_size": self.config.chunk_size,
            "chunk_overlap": self.config.chunk_overlap,
            "uses_tokenizer": self.tokenizer is not None,
            "tokenizer_name": self.tokenizer_name,
            "separators": self.config.separators
        }
        
        if self.tokenizer:
            try:
                info["tokenizer_vocab_size"] = self.tokenizer.vocab_size
                info["tokenizer_max_length"] = getattr(self.tokenizer, 'model_max_length', 'unknown')
            except Exception:
                pass
        
        return info
