#!/usr/bin/env python3
"""
Test runner for the RAG system.
This script runs various tests to validate the system functionality.
"""

import sys
import subprocess
import time
from pathlib import Path

def run_command(command, description, timeout=300):
    """Run a command and return success status."""
    print(f"\n🔄 {description}...")
    print(f"Command: {command}")
    
    try:
        start_time = time.time()
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        
        elapsed = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ {description} completed successfully ({elapsed:.1f}s)")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} failed ({elapsed:.1f}s)")
            print(f"Error: {result.stderr.strip()}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out after {timeout}s")
        return False
    except Exception as e:
        print(f"❌ {description} failed with exception: {e}")
        return False


def test_environment():
    """Test the Python environment and dependencies."""
    print("🧪 Testing Environment")
    print("=" * 50)
    
    tests = [
        ("python --version", "Python version check"),
        ("python -c \"import torch; print(f'PyTorch: {torch.__version__}')\"", "PyTorch installation"),
        ("python -c \"import transformers; print(f'Transformers: {transformers.__version__}')\"", "Transformers installation"),
        ("python -c \"import langchain; print(f'LangChain: {langchain.__version__}')\"", "LangChain installation"),
        ("python -c \"import sentence_transformers; print(f'SentenceTransformers: {sentence_transformers.__version__}')\"", "Sentence Transformers installation"),
    ]
    
    results = []
    for command, description in tests:
        success = run_command(command, description, timeout=30)
        results.append(success)
    
    return all(results)


def test_cuda():
    """Test CUDA availability."""
    print("\n🔥 Testing CUDA")
    print("=" * 50)
    
    cuda_tests = [
        ("python -c \"import torch; print(f'CUDA available: {torch.cuda.is_available()}')\"", "CUDA availability"),
        ("python -c \"import torch; print(f'CUDA devices: {torch.cuda.device_count()}') if torch.cuda.is_available() else print('No CUDA devices')\"", "CUDA device count"),
    ]
    
    results = []
    for command, description in cuda_tests:
        success = run_command(command, description, timeout=30)
        results.append(success)
    
    return all(results)


def test_rag_components():
    """Test individual RAG components."""
    print("\n🔧 Testing RAG Components")
    print("=" * 50)
    
    component_tests = [
        ("python -c \"from rag_system.config import RAGConfig; print('✅ Config import successful')\"", "Configuration import"),
        ("python -c \"from rag_system.core import DocumentLoader; print('✅ DocumentLoader import successful')\"", "DocumentLoader import"),
        ("python -c \"from rag_system.core import TextSplitter; print('✅ TextSplitter import successful')\"", "TextSplitter import"),
        ("python -c \"from rag_system.core import EmbeddingModel; print('✅ EmbeddingModel import successful')\"", "EmbeddingModel import"),
        ("python -c \"from rag_system.core import VectorStore; print('✅ VectorStore import successful')\"", "VectorStore import"),
        ("python -c \"from rag_system.core import RAGPipeline; print('✅ RAGPipeline import successful')\"", "RAGPipeline import"),
    ]
    
    results = []
    for command, description in component_tests:
        success = run_command(command, description, timeout=30)
        results.append(success)
    
    return all(results)


def test_cli():
    """Test CLI functionality."""
    print("\n💻 Testing CLI")
    print("=" * 50)
    
    cli_tests = [
        ("python -m rag_system --help", "CLI help"),
        ("python -m rag_system init-config --output test_config.yaml", "Configuration generation"),
        ("python -m rag_system test", "System test (may take several minutes)"),
    ]
    
    results = []
    for command, description in cli_tests:
        timeout = 600 if "test" in command else 30  # Longer timeout for system test
        success = run_command(command, description, timeout=timeout)
        results.append(success)
    
    # Clean up test files
    test_files = ["test_config.yaml"]
    for file in test_files:
        if Path(file).exists():
            Path(file).unlink()
            print(f"🧹 Cleaned up {file}")
    
    return all(results)


def test_examples():
    """Test example scripts."""
    print("\n📚 Testing Examples")
    print("=" * 50)
    
    example_tests = [
        ("python examples/basic_usage.py", "Basic usage example"),
        ("python examples/custom_models.py", "Custom models example"),
    ]
    
    results = []
    for command, description in example_tests:
        success = run_command(command, description, timeout=600)  # Longer timeout for examples
        results.append(success)
    
    return all(results)


def run_unit_tests():
    """Run unit tests if pytest is available."""
    print("\n🧪 Running Unit Tests")
    print("=" * 50)
    
    # Check if pytest is available
    pytest_check = run_command("python -c \"import pytest; print('pytest available')\"", "Pytest availability", timeout=10)
    
    if not pytest_check:
        print("⚠️  Pytest not available, skipping unit tests")
        print("Install with: pip install pytest")
        return True  # Don't fail if pytest is not available
    
    # Run tests
    test_command = "python -m pytest tests/ -v --tb=short"
    return run_command(test_command, "Unit tests", timeout=600)


def main():
    """Run all tests."""
    print("🚀 RAG System Test Suite")
    print("=" * 60)
    print("This script will test various components of the RAG system.")
    print("Some tests may take several minutes to complete.")
    print("=" * 60)
    
    test_results = {}
    
    # Run test suites
    test_suites = [
        ("Environment", test_environment),
        ("CUDA", test_cuda),
        ("RAG Components", test_rag_components),
        ("CLI", test_cli),
        ("Examples", test_examples),
        ("Unit Tests", run_unit_tests),
    ]
    
    for suite_name, test_func in test_suites:
        try:
            result = test_func()
            test_results[suite_name] = result
        except KeyboardInterrupt:
            print(f"\n⚠️  Test suite '{suite_name}' interrupted by user")
            test_results[suite_name] = False
            break
        except Exception as e:
            print(f"\n❌ Test suite '{suite_name}' failed with exception: {e}")
            test_results[suite_name] = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for suite_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{suite_name:20} {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    
    if all_passed:
        print("🎉 All tests passed! Your RAG system is ready to use.")
        print("\n🚀 Next steps:")
        print("   1. Load your documents: python -m rag_system load -s /path/to/docs")
        print("   2. Start querying: python -m rag_system query --interactive")
        print("   3. Launch web UI: python -m rag_system.web_ui")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("\n🔧 Troubleshooting:")
        print("   1. Make sure all dependencies are installed: pip install -r requirements.txt")
        print("   2. Check your Python version (3.8+ required)")
        print("   3. For CUDA issues, verify your GPU drivers and CUDA installation")
        print("   4. Try running individual components to isolate issues")
        return 1


if __name__ == "__main__":
    sys.exit(main())
