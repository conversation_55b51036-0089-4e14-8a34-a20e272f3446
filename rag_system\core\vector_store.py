"""
Vector store implementations for document storage and retrieval.
"""

import logging
import pickle
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any
from langchain.docstore.document import Document as LangchainDocument
from langchain.vectorstores.base import VectorStore as LangchainVectorStore
from langchain_community.vectorstores import FAISS, Chroma
from ..config import VectorStoreConfig
from .embeddings import EmbeddingModel

logger = logging.getLogger(__name__)


class VectorStore:
    """
    Unified vector store interface supporting multiple backends.
    """
    
    def __init__(self, config: VectorStoreConfig, embedding_model: EmbeddingModel):
        """
        Initialize the vector store.
        
        Args:
            config: Vector store configuration
            embedding_model: Embedding model for document encoding
        """
        self.config = config
        self.embedding_model = embedding_model
        self.store: Optional[LangchainVectorStore] = None
        self.document_metadata: Dict[str, Any] = {}
        
        # Create persist directory if specified
        if config.persist_directory:
            Path(config.persist_directory).mkdir(parents=True, exist_ok=True)
    
    def create_store(self, documents: List[LangchainDocument]) -> None:
        """
        Create a new vector store from documents.
        
        Args:
            documents: List of documents to add to the store
        """
        if not documents:
            raise ValueError("Cannot create store with empty document list")
        
        logger.info(f"Creating {self.config.backend} vector store with {len(documents)} documents...")
        
        try:
            if self.config.backend.lower() == "faiss":
                self.store = FAISS.from_documents(
                    documents=documents,
                    embedding=self.embedding_model
                )
            elif self.config.backend.lower() == "chroma":
                self.store = Chroma.from_documents(
                    documents=documents,
                    embedding=self.embedding_model,
                    collection_name=self.config.collection_name,
                    persist_directory=self.config.persist_directory
                )
            else:
                raise ValueError(f"Unsupported vector store backend: {self.config.backend}")
            
            # Store document metadata
            self._update_metadata(documents)
            
            logger.info(f"Successfully created vector store with {len(documents)} documents")
            
        except Exception as e:
            logger.error(f"Failed to create vector store: {e}")
            raise
    
    def add_documents(self, documents: List[LangchainDocument]) -> None:
        """
        Add documents to an existing vector store.
        
        Args:
            documents: List of documents to add
        """
        if not documents:
            logger.warning("No documents to add")
            return
        
        if self.store is None:
            logger.info("No existing store found, creating new one")
            self.create_store(documents)
            return
        
        logger.info(f"Adding {len(documents)} documents to existing store...")
        
        try:
            self.store.add_documents(documents)
            self._update_metadata(documents)
            logger.info(f"Successfully added {len(documents)} documents")
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            raise
    
    def similarity_search(self, query: str, k: int = 4, 
                         score_threshold: Optional[float] = None) -> List[LangchainDocument]:
        """
        Search for similar documents.
        
        Args:
            query: Search query
            k: Number of documents to return
            score_threshold: Minimum similarity score threshold
            
        Returns:
            List of similar documents
        """
        if self.store is None:
            raise ValueError("Vector store not initialized. Call create_store() first.")
        
        try:
            if score_threshold is not None:
                # Use similarity search with score threshold
                docs_with_scores = self.store.similarity_search_with_score(query, k=k)
                docs = [doc for doc, score in docs_with_scores if score >= score_threshold]
            else:
                docs = self.store.similarity_search(query, k=k)
            
            logger.debug(f"Found {len(docs)} documents for query: {query[:50]}...")
            return docs

        except Exception as e:
            logger.error(f"Error during similarity search: {e}")
            raise

    def similarity_search_with_score(self, query: str, k: int = 4) -> List[Tuple[LangchainDocument, float]]:
        """
        Search for similar documents with similarity scores.

        Args:
            query: Search query
            k: Number of documents to return

        Returns:
            List of (document, score) tuples
        """
        if self.store is None:
            raise ValueError("Vector store not initialized. Call create_store() first.")

        try:
            docs_with_scores = self.store.similarity_search_with_score(query, k=k)
            logger.debug(f"Found {len(docs_with_scores)} documents with scores for query: {query[:50]}...")
            return docs_with_scores

        except Exception as e:
            logger.error(f"Error during similarity search with score: {e}")
            raise

    def save(self, path: Optional[str] = None) -> None:
        """
        Save the vector store to disk.

        Args:
            path: Path to save the store (if None, use config persist_directory)
        """
        if self.store is None:
            raise ValueError("No vector store to save")

        if path is None:
            if not self.config.persist_directory:
                raise ValueError("No persist directory configured")
            path = self.config.persist_directory

        save_path = Path(path)
        save_path.mkdir(parents=True, exist_ok=True)

        try:
            if self.config.backend.lower() == "faiss":
                self.store.save_local(str(save_path))
                # Save metadata separately
                metadata_path = save_path / "metadata.pkl"
                with open(metadata_path, 'wb') as f:
                    pickle.dump(self.document_metadata, f)
            elif self.config.backend.lower() == "chroma":
                # Chroma handles persistence automatically if persist_directory is set
                if hasattr(self.store, 'persist'):
                    self.store.persist()

            logger.info(f"Vector store saved to {save_path}")

        except Exception as e:
            logger.error(f"Failed to save vector store: {e}")
            raise

    def load(self, path: Optional[str] = None) -> None:
        """
        Load a vector store from disk.

        Args:
            path: Path to load the store from (if None, use config persist_directory)
        """
        if path is None:
            if not self.config.persist_directory:
                raise ValueError("No persist directory configured")
            path = self.config.persist_directory

        load_path = Path(path)
        if not load_path.exists():
            raise FileNotFoundError(f"Vector store path not found: {load_path}")

        try:
            if self.config.backend.lower() == "faiss":
                self.store = FAISS.load_local(
                    str(load_path),
                    embeddings=self.embedding_model,
                    allow_dangerous_deserialization=True
                )
                # Load metadata if available
                metadata_path = load_path / "metadata.pkl"
                if metadata_path.exists():
                    with open(metadata_path, 'rb') as f:
                        self.document_metadata = pickle.load(f)
            elif self.config.backend.lower() == "chroma":
                self.store = Chroma(
                    collection_name=self.config.collection_name,
                    embedding_function=self.embedding_model,
                    persist_directory=str(load_path)
                )

            logger.info(f"Vector store loaded from {load_path}")

        except Exception as e:
            logger.error(f"Failed to load vector store: {e}")
            raise

    def _update_metadata(self, documents: List[LangchainDocument]) -> None:
        """Update document metadata tracking."""
        for doc in documents:
            doc_id = hash(doc.page_content)
            self.document_metadata[doc_id] = {
                'source': doc.metadata.get('source', 'unknown'),
                'length': len(doc.page_content),
                'metadata': doc.metadata
            }

    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector store."""
        stats = {
            'backend': self.config.backend,
            'collection_name': self.config.collection_name,
            'persist_directory': self.config.persist_directory,
            'document_count': len(self.document_metadata),
            'embedding_dimension': self.embedding_model.get_embedding_dimension()
        }

        if self.store and hasattr(self.store, 'index'):
            # FAISS specific stats
            try:
                stats['index_size'] = self.store.index.ntotal
            except:
                pass

        return stats

    def delete_documents(self, doc_ids: List[str]) -> None:
        """
        Delete documents by IDs (if supported by backend).

        Args:
            doc_ids: List of document IDs to delete
        """
        if self.store is None:
            raise ValueError("Vector store not initialized")

        try:
            if hasattr(self.store, 'delete'):
                self.store.delete(doc_ids)
                logger.info(f"Deleted {len(doc_ids)} documents")
            else:
                logger.warning(f"Document deletion not supported by {self.config.backend}")

        except Exception as e:
            logger.error(f"Failed to delete documents: {e}")
            raise
