# Vector Store Options for RAG System

The RAG system supports multiple vector store backends, each with different advantages and use cases.

## 🏆 Recommended: ChromaDB (Default)

**Best for**: General use, Windows compatibility, ease of use

### Advantages:
- ✅ **Excellent Windows support** - No compilation issues
- ✅ **Built-in persistence** - Automatic saving and loading
- ✅ **Rich metadata support** - SQL-like queries
- ✅ **Easy installation** - `pip install chromadb`
- ✅ **Active development** - Regular updates and improvements
- ✅ **Good performance** - Suitable for most RAG applications

### Installation:
```bash
pip install chromadb>=0.4.15
```

### Configuration:
```yaml
vector_store:
  backend: "chroma"
  persist_directory: "./vector_store"
  collection_name: "rag_documents"
```

## 🚀 High Performance: FAISS

**Best for**: Large-scale applications, maximum performance

### Advantages:
- ✅ **Extremely fast** - Optimized for similarity search
- ✅ **Memory efficient** - Handles millions of vectors
- ✅ **GPU support** - CUDA acceleration (Linux/Mac)
- ✅ **Multiple index types** - Flat, IVF, HNSW
- ✅ **Facebook-backed** - Proven at scale

### Disadvantages:
- ❌ **Windows GPU issues** - faiss-gpu not available via pip
- ❌ **No built-in persistence** - Manual save/load required
- ❌ **Limited metadata** - Basic document storage

### Installation:
```bash
# CPU version (Windows compatible)
pip install faiss-cpu

# GPU version (Linux/Mac or conda on Windows)
pip install faiss-gpu
# OR with conda on Windows:
conda install -c conda-forge faiss-gpu
```

### Configuration:
```yaml
vector_store:
  backend: "faiss"
  index_type: "flat"  # or "ivf", "hnsw"
  persist_directory: "./vector_store"
```

## 🌐 Production: Qdrant

**Best for**: Production deployments, distributed systems

### Advantages:
- ✅ **Production-ready** - Built for scale
- ✅ **REST API** - Language-agnostic access
- ✅ **Distributed** - Clustering support
- ✅ **Rich filtering** - Advanced query capabilities
- ✅ **Real-time updates** - Live index updates

### Disadvantages:
- ❌ **More complex** - Requires server setup
- ❌ **Resource intensive** - Higher memory usage
- ❌ **Overkill for small projects** - Better for large deployments

### Installation:
```bash
pip install qdrant-client
```

### Configuration:
```yaml
vector_store:
  backend: "qdrant"
  collection_name: "rag_documents"
  # Additional Qdrant-specific config needed
```

## 💡 Simple: In-Memory Store

**Best for**: Development, testing, small datasets

### Advantages:
- ✅ **No dependencies** - Uses only numpy
- ✅ **Simple implementation** - Easy to understand
- ✅ **Cross-platform** - Works everywhere
- ✅ **Lightweight** - Minimal resource usage

### Disadvantages:
- ❌ **Memory limited** - Stores everything in RAM
- ❌ **Slower** - Basic cosine similarity
- ❌ **No persistence** - Manual save/load

### Configuration:
```yaml
vector_store:
  backend: "simple"
  persist_directory: "./vector_store"
```

## 🔧 Installation Guide

### For Windows Users (Recommended):

1. **Use ChromaDB** (easiest):
```bash
pip install chromadb
```

2. **Use FAISS-CPU** (if you need FAISS):
```bash
pip install faiss-cpu
```

3. **For FAISS-GPU on Windows**:
```bash
# Install Anaconda/Miniconda first, then:
conda install -c conda-forge faiss-gpu
```

### For Linux/Mac Users:

1. **ChromaDB** (recommended for most users):
```bash
pip install chromadb
```

2. **FAISS with GPU** (for maximum performance):
```bash
pip install faiss-gpu
```

3. **Qdrant** (for production):
```bash
pip install qdrant-client
# Optional: Run Qdrant server
docker run -p 6333:6333 qdrant/qdrant
```

## 📊 Performance Comparison

| Backend | Speed | Memory | Windows | GPU | Persistence | Metadata |
|---------|-------|--------|---------|-----|-------------|----------|
| Chroma  | Good  | Good   | ✅      | ❌  | ✅          | ✅       |
| FAISS   | Best  | Best   | ⚠️      | ✅  | Manual      | Basic    |
| Qdrant  | Good  | High   | ✅      | ❌  | ✅          | ✅       |
| Simple  | OK    | High   | ✅      | ❌  | Manual      | Basic    |

## 🎯 Recommendations by Use Case

### 🏠 Personal/Development Projects:
- **ChromaDB** - Easy setup, good performance

### 🏢 Small Business Applications:
- **ChromaDB** - Reliable, feature-rich
- **FAISS-CPU** - If maximum speed needed

### 🏭 Enterprise/Production:
- **FAISS-GPU** - Maximum performance
- **Qdrant** - Distributed, scalable

### 🧪 Research/Experimentation:
- **Simple** - Easy to modify and understand
- **ChromaDB** - Good balance of features

## 🔄 Switching Between Backends

You can easily switch between vector stores:

1. **Export your documents**:
```python
# Save document texts and metadata
documents = pipeline.vector_store.get_all_documents()
```

2. **Change configuration**:
```yaml
vector_store:
  backend: "chroma"  # Change this
```

3. **Re-index documents**:
```python
pipeline.load_documents(documents)
```

## 🛠️ Troubleshooting

### ChromaDB Issues:
```bash
# Update to latest version
pip install --upgrade chromadb

# Clear cache if needed
rm -rf ./vector_store
```

### FAISS Issues:
```bash
# For Windows, use CPU version
pip uninstall faiss-gpu
pip install faiss-cpu

# For Linux/Mac with CUDA issues
pip install faiss-cpu  # Fallback
```

### General Issues:
```bash
# Test vector store
python -c "from rag_system.core.vector_stores import VectorStoreFactory; print('✅ Vector stores available')"

# Check configuration
python -m rag_system test
```

## 📝 Summary

For most users, especially on Windows, **ChromaDB is the recommended choice**. It provides the best balance of performance, features, and ease of use. The RAG system will automatically handle the differences between backends, so you can always switch later if your needs change.
