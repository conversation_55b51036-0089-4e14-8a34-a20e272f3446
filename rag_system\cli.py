"""
Command-line interface for the RAG system.
"""

import click
import logging
import sys
from pathlib import Path
from typing import Optional
import yaml
import json

from .config import RAGConfig
from .core.pipeline import RAGPipeline

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), 
              help='Path to configuration file')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, config, verbose):
    """Advanced RAG System - Retrieval Augmented Generation with LangChain"""
    
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Load configuration
    if config:
        try:
            rag_config = RAGConfig.from_file(config)
            click.echo(f"✅ Loaded configuration from {config}")
        except Exception as e:
            click.echo(f"❌ Error loading configuration: {e}", err=True)
            sys.exit(1)
    else:
        # Use default configuration with environment overrides
        rag_config = RAGConfig.from_env()
        click.echo("📝 Using default configuration with environment overrides")
    
    # Store config in context
    ctx.ensure_object(dict)
    ctx.obj['config'] = rag_config


@cli.command()
@click.option('--source', '-s', required=True, 
              help='Source to load documents from (file, directory, URL, or dataset)')
@click.option('--type', '-t', 'source_type', 
              type=click.Choice(['auto', 'file', 'directory', 'url', 'dataset']),
              default='auto', help='Type of source')
@click.option('--save', is_flag=True, help='Save the pipeline after loading')
@click.pass_context
def load(ctx, source, source_type, save):
    """Load documents into the RAG system."""
    config = ctx.obj['config']
    
    click.echo("🚀 Initializing RAG pipeline...")
    pipeline = RAGPipeline(config)
    
    try:
        pipeline.initialize()
        click.echo("✅ Pipeline initialized successfully")
        
        click.echo(f"📄 Loading documents from: {source}")
        doc_count = pipeline.load_documents(source, source_type)
        click.echo(f"✅ Loaded {doc_count} document chunks")
        
        if save:
            click.echo("💾 Saving pipeline...")
            pipeline.save_pipeline()
            click.echo("✅ Pipeline saved")
        
        # Show pipeline stats
        stats = pipeline.get_pipeline_stats()
        click.echo("\n📊 Pipeline Statistics:")
        click.echo(f"  • Vector Store: {stats['config']['vector_store_backend']}")
        click.echo(f"  • Embedding Model: {stats['config']['embedding_model']}")
        click.echo(f"  • LLM Model: {stats['config']['llm_model']}")
        if 'vector_store_stats' in stats:
            click.echo(f"  • Documents: {stats['vector_store_stats']['document_count']}")
        
    except Exception as e:
        click.echo(f"❌ Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--load-path', '-l', help='Path to load saved pipeline from')
@click.option('--interactive', '-i', is_flag=True, help='Start interactive mode')
@click.option('--query', '-q', help='Single query to process')
@click.option('--output', '-o', type=click.Path(), help='Output file for results')
@click.pass_context
def query(ctx, load_path, interactive, query, output):
    """Query the RAG system."""
    config = ctx.obj['config']
    
    click.echo("🚀 Initializing RAG pipeline...")
    pipeline = RAGPipeline(config)
    
    try:
        pipeline.initialize()
        
        # Load saved pipeline if specified
        if load_path:
            click.echo(f"📂 Loading pipeline from: {load_path}")
            pipeline.load_pipeline(load_path)
        elif config.vector_store.persist_directory and Path(config.vector_store.persist_directory).exists():
            click.echo("📂 Loading existing pipeline...")
            pipeline.load_pipeline()
        else:
            click.echo("⚠️  No saved pipeline found. Please load documents first using 'rag-system load'")
            sys.exit(1)
        
        click.echo("✅ Pipeline loaded successfully")
        
        if interactive:
            _interactive_mode(pipeline, output)
        elif query:
            _process_single_query(pipeline, query, output)
        else:
            click.echo("❌ Please specify either --interactive or --query")
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"❌ Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--output', '-o', type=click.Path(), default='rag_config.yaml',
              help='Output configuration file path')
@click.pass_context
def init_config(ctx, output):
    """Generate a default configuration file."""
    try:
        config = RAGConfig()
        config.save_to_file(output)
        click.echo(f"✅ Configuration file created: {output}")
        click.echo("📝 Edit the configuration file to customize your RAG system")
        
    except Exception as e:
        click.echo(f"❌ Error creating configuration: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--load-path', '-l', help='Path to load saved pipeline from')
@click.pass_context
def test(ctx, load_path):
    """Test the RAG system components."""
    config = ctx.obj['config']
    
    click.echo("🧪 Testing RAG pipeline...")
    pipeline = RAGPipeline(config)
    
    try:
        pipeline.initialize()
        click.echo("✅ Pipeline initialization test passed")
        
        # Load pipeline if available
        if load_path:
            pipeline.load_pipeline(load_path)
        elif config.vector_store.persist_directory and Path(config.vector_store.persist_directory).exists():
            try:
                pipeline.load_pipeline()
                click.echo("✅ Pipeline loading test passed")
            except:
                click.echo("⚠️  No saved pipeline found, testing without documents")
        
        # Run comprehensive test
        test_results = pipeline.test_pipeline()
        
        click.echo("\n📊 Test Results:")
        click.echo(f"  • Overall Status: {test_results['overall_status']}")
        click.echo(f"  • Pipeline Initialized: {test_results['pipeline_initialized']}")
        click.echo(f"  • Documents Loaded: {test_results['documents_loaded']}")
        
        # Component test results
        if 'components' in test_results:
            click.echo("\n🔧 Component Tests:")
            for component, result in test_results['components'].items():
                if isinstance(result, dict) and 'success' in result:
                    status = "✅" if result['success'] else "❌"
                    click.echo(f"  • {component.title()}: {status}")
                else:
                    click.echo(f"  • {component.title()}: ✅")
        
        # Full pipeline test
        if 'full_pipeline' in test_results:
            pipeline_result = test_results['full_pipeline']
            status = "✅" if pipeline_result['success'] else "❌"
            click.echo(f"\n🔄 Full Pipeline Test: {status}")
            if pipeline_result['success']:
                click.echo(f"  • Answer Length: {pipeline_result['answer_length']} chars")
                click.echo(f"  • Documents Retrieved: {pipeline_result['documents_retrieved']}")
                click.echo(f"  • Documents Used: {pipeline_result['documents_used']}")
        
        if test_results['overall_status'] == 'success':
            click.echo("\n🎉 All tests passed!")
        else:
            click.echo(f"\n❌ Tests failed: {test_results.get('error', 'Unknown error')}")
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"❌ Test failed: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--load-path', '-l', help='Path to load saved pipeline from')
@click.pass_context
def stats(ctx, load_path):
    """Show pipeline statistics and information."""
    config = ctx.obj['config']
    
    pipeline = RAGPipeline(config)
    
    try:
        pipeline.initialize()
        
        if load_path:
            pipeline.load_pipeline(load_path)
        elif config.vector_store.persist_directory and Path(config.vector_store.persist_directory).exists():
            try:
                pipeline.load_pipeline()
            except:
                pass
        
        stats = pipeline.get_pipeline_stats()
        
        click.echo("📊 RAG Pipeline Statistics")
        click.echo("=" * 50)
        
        # Configuration
        click.echo("\n⚙️  Configuration:")
        for key, value in stats['config'].items():
            click.echo(f"  • {key.replace('_', ' ').title()}: {value}")
        
        # Component information
        if 'embedding_info' in stats:
            click.echo(f"\n🔤 Embedding Model:")
            emb_info = stats['embedding_info']
            click.echo(f"  • Model: {emb_info['model_name']}")
            click.echo(f"  • Device: {emb_info['device']}")
            click.echo(f"  • Dimension: {emb_info['embedding_dimension']}")
        
        if 'llm_info' in stats:
            click.echo(f"\n🤖 Language Model:")
            llm_info = stats['llm_info']
            click.echo(f"  • Model: {llm_info['model_name']}")
            click.echo(f"  • Device: {llm_info['device']}")
            if 'num_parameters' in llm_info:
                params = llm_info['num_parameters'] / 1e6
                click.echo(f"  • Parameters: ~{params:.1f}M")
        
        if 'vector_store_stats' in stats:
            click.echo(f"\n🗄️  Vector Store:")
            vs_stats = stats['vector_store_stats']
            click.echo(f"  • Backend: {vs_stats['backend']}")
            click.echo(f"  • Documents: {vs_stats['document_count']}")
            click.echo(f"  • Embedding Dimension: {vs_stats['embedding_dimension']}")
        
        if 'reranker_info' in stats:
            click.echo(f"\n🔄 Reranker:")
            rerank_info = stats['reranker_info']
            click.echo(f"  • Enabled: {rerank_info['enabled']}")
            if rerank_info['enabled']:
                click.echo(f"  • Model: {rerank_info['model_name']}")
        
    except Exception as e:
        click.echo(f"❌ Error getting stats: {e}", err=True)
        sys.exit(1)


def _interactive_mode(pipeline: RAGPipeline, output_file: Optional[str] = None):
    """Run the pipeline in interactive mode."""
    click.echo("\n🎯 Interactive RAG Mode")
    click.echo("Type 'quit' or 'exit' to stop, 'help' for commands")
    click.echo("-" * 50)

    results = []

    while True:
        try:
            question = click.prompt("\n❓ Your question", type=str)

            if question.lower() in ['quit', 'exit', 'q']:
                break
            elif question.lower() == 'help':
                click.echo("\nAvailable commands:")
                click.echo("  • Type any question to get an answer")
                click.echo("  • 'quit' or 'exit' - Exit interactive mode")
                click.echo("  • 'help' - Show this help message")
                continue
            elif not question.strip():
                continue

            click.echo("🔍 Processing your question...")

            result = pipeline.query(question)

            click.echo(f"\n💡 Answer:")
            click.echo(f"{result['answer']}")

            click.echo(f"\n📚 Sources ({result['final_count']} documents used):")
            for i, doc in enumerate(result['source_documents'][:3]):  # Show top 3 sources
                source = doc.metadata.get('source', 'Unknown')
                click.echo(f"  {i+1}. {source}")

            if result['final_count'] > 3:
                click.echo(f"  ... and {result['final_count'] - 3} more sources")

            # Store result for potential output
            results.append({
                'question': question,
                'answer': result['answer'],
                'sources': [doc.metadata.get('source', 'Unknown') for doc in result['source_documents']],
                'retrieval_count': result['retrieval_count'],
                'final_count': result['final_count']
            })

        except KeyboardInterrupt:
            click.echo("\n\n👋 Goodbye!")
            break
        except Exception as e:
            click.echo(f"❌ Error: {e}", err=True)

    # Save results if output file specified
    if output_file and results:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            click.echo(f"\n💾 Results saved to {output_file}")
        except Exception as e:
            click.echo(f"❌ Error saving results: {e}", err=True)


def _process_single_query(pipeline: RAGPipeline, question: str, output_file: Optional[str] = None):
    """Process a single query."""
    click.echo(f"🔍 Processing question: {question}")

    try:
        result = pipeline.query(question)

        click.echo(f"\n💡 Answer:")
        click.echo(f"{result['answer']}")

        click.echo(f"\n📚 Sources ({result['final_count']} documents used):")
        for i, doc in enumerate(result['source_documents']):
            source = doc.metadata.get('source', 'Unknown')
            click.echo(f"  {i+1}. {source}")

        click.echo(f"\n📊 Statistics:")
        click.echo(f"  • Documents retrieved: {result['retrieval_count']}")
        click.echo(f"  • Documents used: {result['final_count']}")
        click.echo(f"  • Reranked: {result['reranked']}")

        # Save result if output file specified
        if output_file:
            output_data = {
                'question': question,
                'answer': result['answer'],
                'sources': [doc.metadata.get('source', 'Unknown') for doc in result['source_documents']],
                'statistics': {
                    'retrieval_count': result['retrieval_count'],
                    'final_count': result['final_count'],
                    'reranked': result['reranked'],
                    'context_length': result['context_length'],
                    'prompt_length': result['prompt_length']
                }
            }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            click.echo(f"\n💾 Result saved to {output_file}")

    except Exception as e:
        click.echo(f"❌ Error processing query: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    cli()
