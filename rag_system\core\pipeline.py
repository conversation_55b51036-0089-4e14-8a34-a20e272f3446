"""
Main RAG pipeline that orchestrates all components.
"""

import logging
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple, Union
from langchain.docstore.document import Document as LangchainDocument

from ..config import RAGConfig
from .document_loader import DocumentLoader
from .text_splitter import TextSplitter
from .embeddings import EmbeddingModel
from .vector_store import VectorStore
from .retriever import Retriever
from .reranker import Reranker
from .llm_wrapper import LLMWrapper

logger = logging.getLogger(__name__)


class RAGPipeline:
    """
    Complete RAG pipeline integrating all components.
    """
    
    # Default RAG prompt template
    DEFAULT_PROMPT_TEMPLATE = """You are a helpful assistant that answers questions based on the provided context. Use the information from the context to answer the question accurately and concisely.

Context:
{context}

Question: {question}

Answer: """
    
    def __init__(self, config: RAGConfig):
        """
        Initialize the RAG pipeline.
        
        Args:
            config: RAG system configuration
        """
        self.config = config
        self.config.setup_directories()
        
        # Initialize components
        self.document_loader = DocumentLoader()
        self.embedding_model = None
        self.text_splitter = None
        self.vector_store = None
        self.retriever = None
        self.reranker = None
        self.llm = None
        
        # Pipeline state
        self.is_initialized = False
        self.documents_loaded = False
        
        logger.info("RAG pipeline created")
    
    def initialize(self) -> None:
        """Initialize all pipeline components."""
        logger.info("Initializing RAG pipeline components...")
        
        try:
            # Initialize embedding model
            logger.info("Loading embedding model...")
            self.embedding_model = EmbeddingModel(self.config.embedding)
            
            # Initialize text splitter
            logger.info("Setting up text splitter...")
            self.text_splitter = TextSplitter(
                self.config.text_splitter,
                tokenizer_name=self.config.embedding.model_name
            )
            
            # Initialize vector store
            logger.info("Setting up vector store...")
            self.vector_store = VectorStore(self.config.vector_store, self.embedding_model)
            
            # Initialize retriever
            logger.info("Setting up retriever...")
            self.retriever = Retriever(self.config.retriever, self.vector_store)
            
            # Initialize reranker
            logger.info("Setting up reranker...")
            self.reranker = Reranker(self.config.reranker)
            
            # Initialize LLM
            logger.info("Loading LLM...")
            self.llm = LLMWrapper(self.config.llm)
            
            self.is_initialized = True
            logger.info("RAG pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize RAG pipeline: {e}")
            raise
    
    def load_documents(self, source: Union[str, Path, List[str]], 
                      source_type: str = "auto") -> int:
        """
        Load documents into the pipeline.
        
        Args:
            source: Document source (file, directory, URL, or dataset name)
            source_type: Type of source ("file", "directory", "url", "dataset", "auto")
            
        Returns:
            Number of documents loaded
        """
        if not self.is_initialized:
            raise ValueError("Pipeline not initialized. Call initialize() first.")
        
        logger.info(f"Loading documents from {source} (type: {source_type})")
        
        try:
            # Load documents based on source type
            if source_type == "auto":
                source_type = self._detect_source_type(source)
            
            if source_type == "file":
                documents = self.document_loader.load_file(source)
            elif source_type == "directory":
                documents = self.document_loader.load_directory(source)
            elif source_type == "url":
                if isinstance(source, str):
                    source = [source]
                documents = self.document_loader.load_from_urls(source)
            elif source_type == "dataset":
                documents = self.document_loader.load_from_dataset(source)
            else:
                raise ValueError(f"Unsupported source type: {source_type}")
            
            if not documents:
                logger.warning("No documents loaded")
                return 0
            
            # Split documents into chunks
            logger.info(f"Splitting {len(documents)} documents into chunks...")
            chunks = self.text_splitter.split_documents(documents)
            
            # Create or update vector store
            if self.documents_loaded:
                self.vector_store.add_documents(chunks)
            else:
                self.vector_store.create_store(chunks)
                self.documents_loaded = True
            
            logger.info(f"Successfully loaded {len(chunks)} document chunks")
            return len(chunks)
            
        except Exception as e:
            logger.error(f"Failed to load documents: {e}")
            raise
    
    def query(self, question: str, 
              top_k: Optional[int] = None,
              use_reranker: Optional[bool] = None,
              prompt_template: Optional[str] = None) -> Dict[str, Any]:
        """
        Query the RAG system.
        
        Args:
            question: User question
            top_k: Number of documents to retrieve
            use_reranker: Whether to use reranker (if None, use config default)
            prompt_template: Custom prompt template
            
        Returns:
            Dictionary containing answer and metadata
        """
        if not self.is_initialized:
            raise ValueError("Pipeline not initialized. Call initialize() first.")
        
        if not self.documents_loaded:
            raise ValueError("No documents loaded. Call load_documents() first.")
        
        if not question.strip():
            raise ValueError("Question cannot be empty")
        
        logger.info(f"Processing query: {question[:100]}...")
        
        try:
            # Step 1: Retrieve relevant documents
            logger.debug("Retrieving relevant documents...")
            retrieved_docs = self.retriever.retrieve(
                query=question,
                top_k=top_k or self.config.retriever.top_k
            )
            
            if not retrieved_docs:
                logger.warning("No relevant documents found")
                return {
                    "answer": "I couldn't find any relevant information to answer your question.",
                    "source_documents": [],
                    "retrieval_count": 0,
                    "reranked": False
                }
            
            # Step 2: Rerank documents (if enabled)
            use_rerank = use_reranker if use_reranker is not None else self.config.reranker.enabled
            if use_rerank and self.reranker.is_enabled():
                logger.debug("Reranking documents...")
                final_docs = self.reranker.rerank(
                    query=question,
                    documents=retrieved_docs,
                    top_k=self.config.reranker.top_k
                )
                reranked = True
            else:
                final_docs = retrieved_docs[:self.config.reranker.top_k]
                reranked = False
            
            # Step 3: Build context from documents
            context = self._build_context(final_docs)
            
            # Step 4: Generate answer
            logger.debug("Generating answer...")
            template = prompt_template or self.DEFAULT_PROMPT_TEMPLATE
            prompt = template.format(context=context, question=question)
            
            answer = self.llm.generate(prompt)
            
            # Step 5: Prepare response
            response = {
                "answer": answer,
                "source_documents": final_docs,
                "retrieval_count": len(retrieved_docs),
                "final_count": len(final_docs),
                "reranked": reranked,
                "context_length": len(context),
                "prompt_length": self.llm.get_token_count(prompt)
            }
            
            logger.info(f"Query processed successfully. Retrieved {len(retrieved_docs)} docs, used {len(final_docs)} for answer.")
            return response
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            raise
    
    def batch_query(self, questions: List[str], **kwargs) -> List[Dict[str, Any]]:
        """
        Process multiple queries.
        
        Args:
            questions: List of questions
            **kwargs: Additional arguments for query method
            
        Returns:
            List of query results
        """
        results = []
        for question in questions:
            try:
                result = self.query(question, **kwargs)
                results.append(result)
            except Exception as e:
                logger.error(f"Error processing question '{question}': {e}")
                results.append({
                    "answer": f"Error processing question: {str(e)}",
                    "source_documents": [],
                    "error": str(e)
                })
        
        return results
    
    def _detect_source_type(self, source: Union[str, Path]) -> str:
        """Automatically detect the source type."""
        if isinstance(source, (str, Path)):
            path = Path(source)
            if path.exists():
                return "directory" if path.is_dir() else "file"
            elif str(source).startswith(("http://", "https://")):
                return "url"
            else:
                return "dataset"
        else:
            return "url"  # Assume list of URLs
    
    def _build_context(self, documents: List[LangchainDocument]) -> str:
        """Build context string from documents."""
        context_parts = []
        for i, doc in enumerate(documents):
            source = doc.metadata.get('source', 'Unknown')
            context_parts.append(f"Document {i+1} (Source: {source}):\n{doc.page_content}")
        
        return "\n\n".join(context_parts)

    def save_pipeline(self, path: Optional[str] = None) -> None:
        """
        Save the pipeline state.

        Args:
            path: Path to save the pipeline (if None, use config persist_directory)
        """
        if not self.documents_loaded:
            logger.warning("No documents loaded, only saving configuration")

        save_path = path or self.config.vector_store.persist_directory
        if not save_path:
            raise ValueError("No save path specified")

        try:
            # Save vector store
            if self.vector_store and self.documents_loaded:
                self.vector_store.save(save_path)

            # Save configuration
            config_path = Path(save_path) / "config.yaml"
            self.config.save_to_file(str(config_path))

            logger.info(f"Pipeline saved to {save_path}")

        except Exception as e:
            logger.error(f"Failed to save pipeline: {e}")
            raise

    def load_pipeline(self, path: Optional[str] = None) -> None:
        """
        Load a saved pipeline.

        Args:
            path: Path to load the pipeline from
        """
        load_path = path or self.config.vector_store.persist_directory
        if not load_path:
            raise ValueError("No load path specified")

        load_path = Path(load_path)
        if not load_path.exists():
            raise FileNotFoundError(f"Pipeline path not found: {load_path}")

        try:
            # Initialize components if not already done
            if not self.is_initialized:
                self.initialize()

            # Load vector store
            self.vector_store.load(str(load_path))
            self.documents_loaded = True

            logger.info(f"Pipeline loaded from {load_path}")

        except Exception as e:
            logger.error(f"Failed to load pipeline: {e}")
            raise

    def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get comprehensive pipeline statistics."""
        stats = {
            "initialized": self.is_initialized,
            "documents_loaded": self.documents_loaded,
            "config": {
                "embedding_model": self.config.embedding.model_name,
                "llm_model": self.config.llm.model_name,
                "vector_store_backend": self.config.vector_store.backend,
                "reranker_enabled": self.config.reranker.enabled,
                "chunk_size": self.config.text_splitter.chunk_size
            }
        }

        if self.is_initialized:
            if self.embedding_model:
                stats["embedding_info"] = self.embedding_model.get_model_info()

            if self.llm:
                stats["llm_info"] = self.llm.get_model_info()

            if self.vector_store and self.documents_loaded:
                stats["vector_store_stats"] = self.vector_store.get_stats()

            if self.reranker:
                stats["reranker_info"] = self.reranker.get_model_info()

            if self.text_splitter:
                stats["text_splitter_info"] = self.text_splitter.get_chunk_size_info()

        return stats

    def test_pipeline(self, test_query: str = "What is machine learning?") -> Dict[str, Any]:
        """
        Test the entire pipeline with a sample query.

        Args:
            test_query: Query to test with

        Returns:
            Test results and diagnostics
        """
        test_results = {
            "test_query": test_query,
            "pipeline_initialized": self.is_initialized,
            "documents_loaded": self.documents_loaded,
            "components": {}
        }

        try:
            if not self.is_initialized:
                test_results["error"] = "Pipeline not initialized"
                return test_results

            # Test individual components
            if self.embedding_model:
                test_results["components"]["embedding"] = {
                    "status": "loaded",
                    "info": self.embedding_model.get_model_info()
                }

            if self.llm:
                test_results["components"]["llm"] = self.llm.test_generation("Hello")

            if self.reranker:
                test_results["components"]["reranker"] = self.reranker.test_reranking(test_query)

            if self.retriever and self.documents_loaded:
                test_results["components"]["retriever"] = self.retriever.test_retrieval(test_query)

            # Test full pipeline if documents are loaded
            if self.documents_loaded:
                try:
                    result = self.query(test_query)
                    test_results["full_pipeline"] = {
                        "success": True,
                        "answer_length": len(result["answer"]),
                        "documents_retrieved": result["retrieval_count"],
                        "documents_used": result["final_count"],
                        "reranked": result["reranked"]
                    }
                except Exception as e:
                    test_results["full_pipeline"] = {
                        "success": False,
                        "error": str(e)
                    }
            else:
                test_results["full_pipeline"] = {
                    "success": False,
                    "error": "No documents loaded"
                }

            test_results["overall_status"] = "success"
            logger.info("Pipeline test completed")

        except Exception as e:
            test_results["overall_status"] = "failed"
            test_results["error"] = str(e)
            logger.error(f"Pipeline test failed: {e}")

        return test_results
