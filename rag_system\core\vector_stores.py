"""
Alternative vector store implementations with better cross-platform support.
"""

import logging
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
from langchain.docstore.document import Document as LangchainDocument
from langchain.vectorstores.base import VectorStore as LangchainVectorStore

from ..config import VectorStoreConfig
from .embeddings import EmbeddingModel

logger = logging.getLogger(__name__)


class VectorStoreFactory:
    """Factory for creating vector store instances."""
    
    @staticmethod
    def create_vector_store(config: VectorStoreConfig, embedding_model: EmbeddingModel):
        """Create a vector store based on configuration."""
        backend = config.backend.lower()
        
        if backend == "chroma":
            return ChromaVectorStore(config, embedding_model)
        elif backend == "simple":
            return SimpleVectorStore(config, embedding_model)
        elif backend == "faiss":
            return FAISSVectorStore(config, embedding_model)
        elif backend == "qdrant":
            return QdrantVectorStore(config, embedding_model)
        else:
            # Fallback to simple implementation
            logger.warning(f"Unknown backend '{backend}', using simple vector store")
            return SimpleVectorStore(config, embedding_model)


class BaseVectorStore:
    """Base class for vector store implementations."""
    
    def __init__(self, config: VectorStoreConfig, embedding_model: EmbeddingModel):
        self.config = config
        self.embedding_model = embedding_model
        self.documents = []
        self.embeddings = []
        
        if config.persist_directory:
            Path(config.persist_directory).mkdir(parents=True, exist_ok=True)
    
    def add_documents(self, documents: List[LangchainDocument]) -> None:
        """Add documents to the store."""
        raise NotImplementedError
    
    def similarity_search(self, query: str, k: int = 4) -> List[LangchainDocument]:
        """Search for similar documents."""
        raise NotImplementedError
    
    def save(self, path: str) -> None:
        """Save the vector store."""
        raise NotImplementedError
    
    def load(self, path: str) -> None:
        """Load the vector store."""
        raise NotImplementedError


class ChromaVectorStore(BaseVectorStore):
    """Chroma vector store implementation."""
    
    def __init__(self, config: VectorStoreConfig, embedding_model: EmbeddingModel):
        super().__init__(config, embedding_model)
        self.store = None
        self._initialize_chroma()
    
    def _initialize_chroma(self):
        """Initialize Chroma vector store."""
        try:
            from langchain_community.vectorstores import Chroma
            self.chroma_class = Chroma
            logger.info("Chroma vector store initialized")
        except ImportError:
            raise ImportError("Chroma not available. Install with: pip install chromadb")
    
    def add_documents(self, documents: List[LangchainDocument]) -> None:
        """Add documents to Chroma store."""
        if not documents:
            return
        
        if self.store is None:
            self.store = self.chroma_class.from_documents(
                documents=documents,
                embedding=self.embedding_model,
                collection_name=self.config.collection_name,
                persist_directory=self.config.persist_directory
            )
        else:
            self.store.add_documents(documents)
        
        logger.info(f"Added {len(documents)} documents to Chroma store")
    
    def similarity_search(self, query: str, k: int = 4) -> List[LangchainDocument]:
        """Search for similar documents in Chroma."""
        if self.store is None:
            return []
        
        return self.store.similarity_search(query, k=k)
    
    def similarity_search_with_score(self, query: str, k: int = 4) -> List[Tuple[LangchainDocument, float]]:
        """Search with similarity scores."""
        if self.store is None:
            return []
        
        return self.store.similarity_search_with_score(query, k=k)
    
    def save(self, path: str) -> None:
        """Save Chroma store (automatic persistence)."""
        if self.store and hasattr(self.store, 'persist'):
            self.store.persist()
        logger.info("Chroma store persisted")
    
    def load(self, path: str) -> None:
        """Load Chroma store."""
        self.store = self.chroma_class(
            collection_name=self.config.collection_name,
            embedding_function=self.embedding_model,
            persist_directory=path
        )
        logger.info(f"Chroma store loaded from {path}")


class SimpleVectorStore(BaseVectorStore):
    """Simple in-memory vector store using cosine similarity."""
    
    def __init__(self, config: VectorStoreConfig, embedding_model: EmbeddingModel):
        super().__init__(config, embedding_model)
        import numpy as np
        self.np = np
    
    def add_documents(self, documents: List[LangchainDocument]) -> None:
        """Add documents to simple store."""
        if not documents:
            return
        
        # Extract text and compute embeddings
        texts = [doc.page_content for doc in documents]
        new_embeddings = self.embedding_model.embed_documents(texts)
        
        # Store documents and embeddings
        self.documents.extend(documents)
        self.embeddings.extend(new_embeddings)
        
        logger.info(f"Added {len(documents)} documents to simple store")
    
    def similarity_search(self, query: str, k: int = 4) -> List[LangchainDocument]:
        """Search using cosine similarity."""
        if not self.documents:
            return []
        
        # Get query embedding
        query_embedding = self.embedding_model.embed_query(query)
        
        # Calculate similarities
        similarities = []
        for doc_embedding in self.embeddings:
            similarity = self._cosine_similarity(query_embedding, doc_embedding)
            similarities.append(similarity)
        
        # Get top-k most similar documents
        top_indices = self.np.argsort(similarities)[-k:][::-1]
        
        return [self.documents[i] for i in top_indices]
    
    def similarity_search_with_score(self, query: str, k: int = 4) -> List[Tuple[LangchainDocument, float]]:
        """Search with similarity scores."""
        if not self.documents:
            return []
        
        query_embedding = self.embedding_model.embed_query(query)
        
        doc_scores = []
        for i, doc_embedding in enumerate(self.embeddings):
            similarity = self._cosine_similarity(query_embedding, doc_embedding)
            doc_scores.append((self.documents[i], similarity))
        
        # Sort by similarity and return top-k
        doc_scores.sort(key=lambda x: x[1], reverse=True)
        return doc_scores[:k]
    
    def _cosine_similarity(self, a: List[float], b: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        a = self.np.array(a)
        b = self.np.array(b)
        
        dot_product = self.np.dot(a, b)
        norm_a = self.np.linalg.norm(a)
        norm_b = self.np.linalg.norm(b)
        
        if norm_a == 0 or norm_b == 0:
            return 0.0
        
        return dot_product / (norm_a * norm_b)
    
    def save(self, path: str) -> None:
        """Save simple store to disk."""
        import pickle
        
        save_path = Path(path)
        save_path.mkdir(parents=True, exist_ok=True)
        
        data = {
            'documents': self.documents,
            'embeddings': self.embeddings,
            'config': self.config.model_dump()
        }
        
        with open(save_path / 'simple_store.pkl', 'wb') as f:
            pickle.dump(data, f)
        
        logger.info(f"Simple store saved to {save_path}")
    
    def load(self, path: str) -> None:
        """Load simple store from disk."""
        import pickle
        
        load_path = Path(path) / 'simple_store.pkl'
        
        if not load_path.exists():
            raise FileNotFoundError(f"Simple store not found: {load_path}")
        
        with open(load_path, 'rb') as f:
            data = pickle.load(f)
        
        self.documents = data['documents']
        self.embeddings = data['embeddings']
        
        logger.info(f"Simple store loaded from {path}")


class FAISSVectorStore(BaseVectorStore):
    """FAISS vector store implementation (optional)."""
    
    def __init__(self, config: VectorStoreConfig, embedding_model: EmbeddingModel):
        super().__init__(config, embedding_model)
        self.store = None
        self._initialize_faiss()
    
    def _initialize_faiss(self):
        """Initialize FAISS if available."""
        try:
            from langchain_community.vectorstores import FAISS
            self.faiss_class = FAISS
            logger.info("FAISS vector store initialized")
        except ImportError:
            raise ImportError("FAISS not available. Install with: pip install faiss-cpu")
    
    def add_documents(self, documents: List[LangchainDocument]) -> None:
        """Add documents to FAISS store."""
        if not documents:
            return
        
        if self.store is None:
            self.store = self.faiss_class.from_documents(
                documents=documents,
                embedding=self.embedding_model
            )
        else:
            self.store.add_documents(documents)
        
        logger.info(f"Added {len(documents)} documents to FAISS store")
    
    def similarity_search(self, query: str, k: int = 4) -> List[LangchainDocument]:
        """Search in FAISS store."""
        if self.store is None:
            return []
        
        return self.store.similarity_search(query, k=k)
    
    def similarity_search_with_score(self, query: str, k: int = 4) -> List[Tuple[LangchainDocument, float]]:
        """Search with scores in FAISS."""
        if self.store is None:
            return []
        
        return self.store.similarity_search_with_score(query, k=k)
    
    def save(self, path: str) -> None:
        """Save FAISS store."""
        if self.store:
            self.store.save_local(path)
        logger.info(f"FAISS store saved to {path}")
    
    def load(self, path: str) -> None:
        """Load FAISS store."""
        self.store = self.faiss_class.load_local(
            path, 
            embeddings=self.embedding_model,
            allow_dangerous_deserialization=True
        )
        logger.info(f"FAISS store loaded from {path}")


class QdrantVectorStore(BaseVectorStore):
    """Qdrant vector store implementation (optional)."""
    
    def __init__(self, config: VectorStoreConfig, embedding_model: EmbeddingModel):
        super().__init__(config, embedding_model)
        self.store = None
        self._initialize_qdrant()
    
    def _initialize_qdrant(self):
        """Initialize Qdrant if available."""
        try:
            from langchain_community.vectorstores import Qdrant
            from qdrant_client import QdrantClient
            self.qdrant_class = Qdrant
            self.client_class = QdrantClient
            logger.info("Qdrant vector store initialized")
        except ImportError:
            raise ImportError("Qdrant not available. Install with: pip install qdrant-client")
    
    def add_documents(self, documents: List[LangchainDocument]) -> None:
        """Add documents to Qdrant store."""
        if not documents:
            return
        
        if self.store is None:
            # Use in-memory Qdrant for simplicity
            client = self.client_class(":memory:")
            self.store = self.qdrant_class.from_documents(
                documents=documents,
                embedding=self.embedding_model,
                client=client,
                collection_name=self.config.collection_name
            )
        else:
            self.store.add_documents(documents)
        
        logger.info(f"Added {len(documents)} documents to Qdrant store")
    
    def similarity_search(self, query: str, k: int = 4) -> List[LangchainDocument]:
        """Search in Qdrant store."""
        if self.store is None:
            return []
        
        return self.store.similarity_search(query, k=k)
    
    def similarity_search_with_score(self, query: str, k: int = 4) -> List[Tuple[LangchainDocument, float]]:
        """Search with scores in Qdrant."""
        if self.store is None:
            return []
        
        return self.store.similarity_search_with_score(query, k=k)
    
    def save(self, path: str) -> None:
        """Save Qdrant store (depends on client configuration)."""
        logger.info("Qdrant persistence handled by client configuration")
    
    def load(self, path: str) -> None:
        """Load Qdrant store (depends on client configuration)."""
        logger.warning("Qdrant loading from file not implemented")
        raise NotImplementedError("Qdrant file-based loading not supported")
