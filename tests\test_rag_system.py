#!/usr/bin/env python3
"""
Test suite for the RAG system.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
import sys

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from rag_system import RAGConfig, RAGPipeline
from rag_system.core import (
    DocumentLoader, TextSplitter, EmbeddingModel, 
    VectorStore, Retriever, Reranker, LLMWrapper
)


class TestRAGConfig:
    """Test RAG configuration."""
    
    def test_default_config(self):
        """Test default configuration creation."""
        config = RAGConfig()
        assert config.embedding.model_name == "thenlper/gte-small"
        assert config.llm.model_name == "microsoft/DialoGPT-medium"
        assert config.vector_store.backend == "faiss"
    
    def test_config_from_dict(self):
        """Test configuration from dictionary."""
        config_dict = {
            "embedding": {"model_name": "test-model"},
            "llm": {"model_name": "test-llm"}
        }
        config = RAGConfig(**config_dict)
        assert config.embedding.model_name == "test-model"
        assert config.llm.model_name == "test-llm"
    
    def test_config_save_load(self):
        """Test configuration save and load."""
        config = RAGConfig()
        config.embedding.model_name = "test-embedding"
        
        with tempfile.NamedTemporaryFile(suffix='.yaml', delete=False) as f:
            config.save_to_file(f.name)
            loaded_config = RAGConfig.from_file(f.name)
            assert loaded_config.embedding.model_name == "test-embedding"
            Path(f.name).unlink()


class TestDocumentLoader:
    """Test document loader."""
    
    def setup_method(self):
        """Set up test environment."""
        self.loader = DocumentLoader()
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_load_text_file(self):
        """Test loading a text file."""
        test_file = self.temp_dir / "test.txt"
        test_content = "This is a test document."
        test_file.write_text(test_content)
        
        documents = self.loader.load_file(test_file)
        assert len(documents) == 1
        assert test_content in documents[0].page_content
        assert documents[0].metadata['source'] == str(test_file)
    
    def test_load_directory(self):
        """Test loading documents from directory."""
        # Create test files
        (self.temp_dir / "doc1.txt").write_text("Document 1 content")
        (self.temp_dir / "doc2.txt").write_text("Document 2 content")
        
        documents = self.loader.load_directory(self.temp_dir)
        assert len(documents) == 2
        
        contents = [doc.page_content for doc in documents]
        assert "Document 1 content" in contents
        assert "Document 2 content" in contents
    
    def test_supported_extensions(self):
        """Test getting supported file extensions."""
        extensions = self.loader.get_supported_extensions()
        assert '.txt' in extensions
        assert '.pdf' in extensions
        assert '.md' in extensions


class TestTextSplitter:
    """Test text splitter."""
    
    def setup_method(self):
        """Set up test environment."""
        from rag_system.config import TextSplitterConfig
        config = TextSplitterConfig(chunk_size=100, chunk_overlap=10)
        self.splitter = TextSplitter(config)
    
    def test_split_text(self):
        """Test text splitting."""
        long_text = "This is a long text. " * 20  # Create long text
        chunks = self.splitter.split_text(long_text)
        
        assert len(chunks) > 1
        for chunk in chunks:
            assert len(chunk.page_content) <= 150  # Allow some flexibility
    
    def test_remove_duplicates(self):
        """Test duplicate removal."""
        from langchain.docstore.document import Document as LangchainDocument
        
        # Create documents with duplicate content
        docs = [
            LangchainDocument(page_content="Same content", metadata={}),
            LangchainDocument(page_content="Different content", metadata={}),
            LangchainDocument(page_content="Same content", metadata={}),
        ]
        
        unique_docs = self.splitter._remove_duplicates(docs)
        assert len(unique_docs) == 2
        
        contents = [doc.page_content for doc in unique_docs]
        assert "Same content" in contents
        assert "Different content" in contents


class TestRAGPipeline:
    """Test RAG pipeline integration."""
    
    def setup_method(self):
        """Set up test environment."""
        # Use minimal configuration for testing
        self.config = RAGConfig()
        self.config.embedding.model_name = "sentence-transformers/all-MiniLM-L6-v2"
        self.config.llm.model_name = "microsoft/DialoGPT-small"
        self.config.reranker.enabled = False  # Disable for faster testing
        self.config.text_splitter.chunk_size = 100
        
        self.temp_dir = Path(tempfile.mkdtemp())
        self.config.vector_store.persist_directory = str(self.temp_dir / "vector_store")
        
        self.pipeline = RAGPipeline(self.config)
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    @pytest.mark.slow
    def test_pipeline_initialization(self):
        """Test pipeline initialization."""
        self.pipeline.initialize()
        assert self.pipeline.is_initialized
        assert self.pipeline.embedding_model is not None
        assert self.pipeline.llm is not None
    
    @pytest.mark.slow
    def test_document_loading(self):
        """Test document loading."""
        # Create test documents
        docs_dir = self.temp_dir / "docs"
        docs_dir.mkdir()
        (docs_dir / "test1.txt").write_text("Machine learning is a subset of AI.")
        (docs_dir / "test2.txt").write_text("Deep learning uses neural networks.")
        
        self.pipeline.initialize()
        doc_count = self.pipeline.load_documents(str(docs_dir), "directory")
        
        assert doc_count > 0
        assert self.pipeline.documents_loaded
    
    @pytest.mark.slow
    def test_query_processing(self):
        """Test query processing."""
        # Create test documents
        docs_dir = self.temp_dir / "docs"
        docs_dir.mkdir()
        (docs_dir / "ml.txt").write_text(
            "Machine learning is a method of data analysis that automates "
            "analytical model building. It uses algorithms that iteratively "
            "learn from data without being explicitly programmed."
        )
        
        self.pipeline.initialize()
        self.pipeline.load_documents(str(docs_dir), "directory")
        
        result = self.pipeline.query("What is machine learning?")
        
        assert 'answer' in result
        assert 'source_documents' in result
        assert len(result['answer']) > 0
        assert result['retrieval_count'] > 0
    
    def test_pipeline_stats(self):
        """Test pipeline statistics."""
        stats = self.pipeline.get_pipeline_stats()
        
        assert 'initialized' in stats
        assert 'documents_loaded' in stats
        assert 'config' in stats
        assert stats['config']['embedding_model'] == self.config.embedding.model_name


class TestEndToEnd:
    """End-to-end integration tests."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # Create test configuration
        self.config = RAGConfig()
        self.config.embedding.model_name = "sentence-transformers/all-MiniLM-L6-v2"
        self.config.llm.model_name = "microsoft/DialoGPT-small"
        self.config.reranker.enabled = False
        self.config.vector_store.persist_directory = str(self.temp_dir / "vector_store")
        self.config.text_splitter.chunk_size = 200
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    @pytest.mark.slow
    def test_complete_workflow(self):
        """Test complete RAG workflow."""
        # 1. Create sample documents
        docs_dir = self.temp_dir / "documents"
        docs_dir.mkdir()
        
        sample_docs = {
            "ai_basics.txt": (
                "Artificial Intelligence (AI) is the simulation of human "
                "intelligence in machines. It includes machine learning, "
                "natural language processing, and computer vision."
            ),
            "ml_intro.txt": (
                "Machine Learning is a subset of AI that enables computers "
                "to learn and improve from experience without being explicitly "
                "programmed. It uses statistical techniques and algorithms."
            ),
            "dl_overview.txt": (
                "Deep Learning is a subset of machine learning that uses "
                "artificial neural networks with multiple layers. It's "
                "particularly effective for image and speech recognition."
            )
        }
        
        for filename, content in sample_docs.items():
            (docs_dir / filename).write_text(content)
        
        # 2. Initialize and load documents
        pipeline = RAGPipeline(self.config)
        pipeline.initialize()
        
        doc_count = pipeline.load_documents(str(docs_dir), "directory")
        assert doc_count > 0
        
        # 3. Test various queries
        test_queries = [
            "What is artificial intelligence?",
            "How does machine learning work?",
            "What is deep learning?",
            "What are the differences between AI, ML, and DL?"
        ]
        
        for query in test_queries:
            result = pipeline.query(query)
            
            # Verify result structure
            assert 'answer' in result
            assert 'source_documents' in result
            assert 'retrieval_count' in result
            assert 'final_count' in result
            
            # Verify content
            assert len(result['answer']) > 0
            assert result['retrieval_count'] > 0
            assert len(result['source_documents']) > 0
        
        # 4. Test pipeline persistence
        pipeline.save_pipeline()
        
        # 5. Load saved pipeline
        new_pipeline = RAGPipeline(self.config)
        new_pipeline.initialize()
        new_pipeline.load_pipeline()
        
        assert new_pipeline.documents_loaded
        
        # 6. Test query on loaded pipeline
        result = new_pipeline.query("What is AI?")
        assert len(result['answer']) > 0
    
    @pytest.mark.slow
    def test_batch_processing(self):
        """Test batch query processing."""
        # Create test documents
        docs_dir = self.temp_dir / "documents"
        docs_dir.mkdir()
        (docs_dir / "test.txt").write_text(
            "Python is a programming language. "
            "JavaScript is used for web development. "
            "Machine learning is a subset of AI."
        )
        
        # Initialize pipeline
        pipeline = RAGPipeline(self.config)
        pipeline.initialize()
        pipeline.load_documents(str(docs_dir), "directory")
        
        # Test batch queries
        queries = [
            "What is Python?",
            "What is JavaScript?",
            "What is machine learning?"
        ]
        
        results = pipeline.batch_query(queries)
        
        assert len(results) == len(queries)
        for result in results:
            assert 'answer' in result
            assert len(result['answer']) > 0


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
