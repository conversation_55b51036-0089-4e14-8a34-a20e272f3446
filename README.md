# 🤖 Advanced RAG System with LangChain

A modular, user-friendly RAG (Retrieval Augmented Generation) system based on the [HuggingFace Advanced RAG Cookbook](https://huggingface.co/learn/cookbook/advanced_rag). This implementation provides a complete, production-ready RAG pipeline with CUDA support, multiple vector database backends, and both CLI and web interfaces.

## ✨ Features

- **🔧 Modular Architecture**: Easy to customize and extend individual components
- **⚡ CUDA Support**: GPU acceleration for embeddings and LLM inference
- **📚 Multiple Document Formats**: PDF, DOCX, TXT, MD, CSV, HTML support
- **🗄️ Vector Database Options**: ChromaDB (default), FAISS, and Qdrant backends
- **🔄 Document Reranking**: ColBERT-based reranking for improved relevance
- **🎯 User-Friendly Interfaces**: Both CLI and web UI available
- **⚙️ Comprehensive Configuration**: YAML-based configuration management
- **🧪 Built-in Testing**: Component and pipeline testing capabilities

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd rag-docs-langchain

# Set up Python environment (requires Python 3.8+)
python setup_env.py

# Activate virtual environment
# On Windows:
.venv\Scripts\activate
# On Unix/Linux/MacOS:
source .venv/bin/activate
```

### 2. Install Dependencies

```bash
# Install all dependencies
pip install -r requirements.txt

# For CUDA support (if you have compatible GPU):
pip uninstall faiss-cpu
pip install faiss-gpu
```

### 3. Basic Usage

#### Command Line Interface

```bash
# Initialize configuration
python -m rag_system init-config

# Load documents
python -m rag_system load -s ./data/documents -t directory

# Query the system
python -m rag_system query -q "What is machine learning?"

# Interactive mode
python -m rag_system query --interactive

# Test the system
python -m rag_system test
```

#### Web Interface

```bash
# Start web interface
python -m rag_system.web_ui

# Or with custom configuration
python -c "from rag_system.web_ui import create_web_ui; create_web_ui('config.yaml')"
```

#### Python API

```python
from rag_system import RAGConfig, RAGPipeline

# Load configuration
config = RAGConfig.from_file("config.yaml")

# Initialize pipeline
pipeline = RAGPipeline(config)
pipeline.initialize()

# Load documents
pipeline.load_documents("./data/documents", "directory")

# Query
result = pipeline.query("What is machine learning?")
print(result["answer"])
```

## 📋 Requirements

- **Python**: 3.8 or higher
- **Memory**: 4GB+ RAM recommended
- **GPU**: CUDA 11.8+ compatible GPU (optional but recommended)
- **Storage**: 2GB+ free space for models and data

## 🔧 Configuration

The system uses YAML configuration files. Generate a default configuration:

```bash
python -m rag_system init-config
```

Key configuration sections:

- **embedding**: Embedding model settings
- **vector_store**: Vector database configuration
- **text_splitter**: Document chunking parameters
- **retriever**: Document retrieval settings
- **reranker**: Reranking model configuration
- **llm**: Language model settings

## 📖 Documentation

### Architecture Overview

The RAG system consists of several modular components:

1. **Document Loader**: Handles multiple file formats and sources
2. **Text Splitter**: Intelligent document chunking with token awareness
3. **Embedding Model**: Sentence transformer for document embeddings
4. **Vector Store**: Efficient similarity search with multiple backends
5. **Retriever**: Advanced document retrieval with MMR support
6. **Reranker**: ColBERT-based reranking for improved relevance
7. **LLM Wrapper**: Language model with CUDA optimization
8. **RAG Pipeline**: Orchestrates all components

### Component Details

#### Document Loader
- Supports PDF, DOCX, TXT, MD, CSV, HTML files
- Can load from directories, URLs, or HuggingFace datasets
- Automatic file type detection
- Metadata preservation

#### Text Splitter
- Token-aware chunking using model tokenizers
- Hierarchical splitting with markdown awareness
- Configurable chunk size and overlap
- Duplicate removal

#### Vector Store
- ChromaDB for excellent Windows compatibility and ease of use (default)
- FAISS backend for high-performance similarity search
- Qdrant for production deployments
- Automatic persistence and loading

#### Reranker
- ColBERT v2 model for cross-encoder reranking
- Improves relevance of retrieved documents
- Configurable top-k selection
- Optional - can be disabled for faster inference

## 🛠️ Advanced Usage

### Custom Models

You can use any HuggingFace model by updating the configuration:

```yaml
# For embeddings
embedding:
  model_name: "sentence-transformers/all-MiniLM-L6-v2"

# For LLM
llm:
  model_name: "microsoft/DialoGPT-large"
  load_in_4bit: true  # Enable 4-bit quantization
```

### Environment Variables

Override configuration with environment variables:

```bash
export RAG_EMBEDDING_MODEL="sentence-transformers/all-MiniLM-L6-v2"
export RAG_LLM_MODEL="microsoft/DialoGPT-large"
export RAG_VECTOR_STORE="chroma"
export RAG_USE_CUDA="true"
```

### Batch Processing

Process multiple documents or queries:

```python
# Batch document loading
sources = ["doc1.pdf", "doc2.txt", "doc3.md"]
for source in sources:
    pipeline.load_documents(source)

# Batch querying
questions = ["What is AI?", "How does ML work?", "What is deep learning?"]
results = pipeline.batch_query(questions)
```

### Custom Prompt Templates

Customize the RAG prompt:

```python
custom_template = """Based on the following context, answer the question.

Context: {context}

Question: {question}

Provide a detailed answer with examples:"""

result = pipeline.query(
    "What is machine learning?",
    prompt_template=custom_template
)
```

## 🧪 Testing

Run comprehensive tests:

```bash
# Test all components
python -m rag_system test

# Test with custom configuration
python -m rag_system test -c custom_config.yaml

# Get detailed statistics
python -m rag_system stats
```

## 🔍 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   ```yaml
   llm:
     load_in_4bit: true  # Enable quantization
     batch_size: 1       # Reduce batch size
   ```

2. **Slow Performance**
   ```yaml
   reranker:
     enabled: false      # Disable reranking
   retriever:
     top_k: 10          # Reduce retrieved documents
   ```

3. **Model Loading Errors**
   ```bash
   # Clear cache and reinstall
   pip cache purge
   pip install --force-reinstall transformers
   ```

### Performance Optimization

- Use GPU acceleration when available
- Enable model quantization for large models
- Adjust chunk size based on your documents
- Use appropriate vector store backend for your scale

## 📊 Benchmarks

Performance on a typical setup (RTX 3080, 16GB RAM):

| Component | Time | Memory |
|-----------|------|--------|
| Document Loading (100 PDFs) | ~2 minutes | ~2GB |
| Embedding Generation | ~30 seconds | ~4GB |
| Query Processing | ~1-3 seconds | ~6GB |
| With Reranking | ~2-5 seconds | ~8GB |

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [HuggingFace](https://huggingface.co/) for the excellent cookbook and models
- [LangChain](https://langchain.com/) for the RAG framework
- [RAGatouille](https://github.com/bclavie/RAGatouille) for ColBERT reranking
- [Sentence Transformers](https://www.sbert.net/) for embedding models

## 📞 Support

- 📖 [Documentation](docs/)
- 🐛 [Issue Tracker](issues/)
- 💬 [Discussions](discussions/)

---

**Happy RAG-ing! 🚀**