#!/usr/bin/env python3
"""
Example showing how to use custom models in the RAG system.
"""

import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from rag_system import RAGConfig, RAGPipeline


def example_with_different_models():
    """Example using different model configurations."""
    
    print("🔧 Custom Models Example")
    print("=" * 40)
    
    # Configuration 1: Lightweight models for CPU
    print("\n1. Lightweight CPU Configuration:")
    cpu_config = RAGConfig()
    cpu_config.embedding.model_name = "sentence-transformers/all-MiniLM-L6-v2"
    cpu_config.embedding.device = "cpu"
    cpu_config.llm.model_name = "microsoft/DialoGPT-small"
    cpu_config.llm.device = "cpu"
    cpu_config.reranker.enabled = False  # Disable for speed
    
    print(f"   • Embedding: {cpu_config.embedding.model_name}")
    print(f"   • LLM: {cpu_config.llm.model_name}")
    print(f"   • Device: CPU only")
    print(f"   • Reranker: Disabled")
    
    # Configuration 2: High-performance GPU models
    print("\n2. High-Performance GPU Configuration:")
    gpu_config = RAGConfig()
    gpu_config.embedding.model_name = "sentence-transformers/all-mpnet-base-v2"
    gpu_config.embedding.device = "cuda"
    gpu_config.llm.model_name = "microsoft/DialoGPT-large"
    gpu_config.llm.device = "cuda"
    gpu_config.llm.load_in_4bit = True  # Enable quantization
    gpu_config.reranker.enabled = True
    
    print(f"   • Embedding: {gpu_config.embedding.model_name}")
    print(f"   • LLM: {gpu_config.llm.model_name}")
    print(f"   • Device: CUDA")
    print(f"   • Quantization: 4-bit")
    print(f"   • Reranker: Enabled")
    
    # Configuration 3: Multilingual models
    print("\n3. Multilingual Configuration:")
    multilingual_config = RAGConfig()
    multilingual_config.embedding.model_name = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
    multilingual_config.llm.model_name = "microsoft/DialoGPT-medium"
    multilingual_config.reranker.enabled = True
    
    print(f"   • Embedding: {multilingual_config.embedding.model_name}")
    print(f"   • LLM: {multilingual_config.llm.model_name}")
    print(f"   • Languages: Multilingual support")
    
    # Save configurations
    cpu_config.save_to_file("config_cpu.yaml")
    gpu_config.save_to_file("config_gpu.yaml")
    multilingual_config.save_to_file("config_multilingual.yaml")
    
    print("\n💾 Configurations saved:")
    print("   • config_cpu.yaml - Lightweight CPU setup")
    print("   • config_gpu.yaml - High-performance GPU setup")
    print("   • config_multilingual.yaml - Multilingual support")
    
    print("\n🚀 Usage examples:")
    print("   python -m rag_system -c config_cpu.yaml load -s ./docs")
    print("   python -m rag_system -c config_gpu.yaml query -q 'Your question'")
    print("   python -m rag_system -c config_multilingual.yaml query --interactive")


def example_with_custom_chunking():
    """Example with custom text chunking strategies."""
    
    print("\n\n📄 Custom Chunking Example")
    print("=" * 40)
    
    # Small chunks for precise retrieval
    precise_config = RAGConfig()
    precise_config.text_splitter.chunk_size = 256
    precise_config.text_splitter.chunk_overlap = 25
    precise_config.retriever.top_k = 10
    
    print("\n1. Precise Retrieval (Small Chunks):")
    print(f"   • Chunk size: {precise_config.text_splitter.chunk_size} tokens")
    print(f"   • Overlap: {precise_config.text_splitter.chunk_overlap} tokens")
    print(f"   • Retrieval: {precise_config.retriever.top_k} documents")
    print("   • Best for: Factual Q&A, specific information")
    
    # Large chunks for context-rich responses
    contextual_config = RAGConfig()
    contextual_config.text_splitter.chunk_size = 1024
    contextual_config.text_splitter.chunk_overlap = 100
    contextual_config.retriever.top_k = 5
    
    print("\n2. Contextual Retrieval (Large Chunks):")
    print(f"   • Chunk size: {contextual_config.text_splitter.chunk_size} tokens")
    print(f"   • Overlap: {contextual_config.text_splitter.chunk_overlap} tokens")
    print(f"   • Retrieval: {contextual_config.retriever.top_k} documents")
    print("   • Best for: Explanatory answers, complex topics")
    
    # Balanced approach
    balanced_config = RAGConfig()
    balanced_config.text_splitter.chunk_size = 512
    balanced_config.text_splitter.chunk_overlap = 50
    balanced_config.retriever.top_k = 7
    
    print("\n3. Balanced Approach:")
    print(f"   • Chunk size: {balanced_config.text_splitter.chunk_size} tokens")
    print(f"   • Overlap: {balanced_config.text_splitter.chunk_overlap} tokens")
    print(f"   • Retrieval: {balanced_config.retriever.top_k} documents")
    print("   • Best for: General purpose RAG")
    
    # Save chunking configurations
    precise_config.save_to_file("config_precise.yaml")
    contextual_config.save_to_file("config_contextual.yaml")
    balanced_config.save_to_file("config_balanced.yaml")
    
    print("\n💾 Chunking configurations saved:")
    print("   • config_precise.yaml - Small chunks, precise retrieval")
    print("   • config_contextual.yaml - Large chunks, rich context")
    print("   • config_balanced.yaml - Balanced approach")


def example_with_vector_stores():
    """Example with different vector store backends."""
    
    print("\n\n🗄️ Vector Store Backends Example")
    print("=" * 40)
    
    # FAISS for high performance
    faiss_config = RAGConfig()
    faiss_config.vector_store.backend = "faiss"
    faiss_config.vector_store.index_type = "flat"
    faiss_config.vector_store.persist_directory = "./vector_store_faiss"
    
    print("\n1. FAISS Backend:")
    print(f"   • Backend: {faiss_config.vector_store.backend}")
    print(f"   • Index type: {faiss_config.vector_store.index_type}")
    print("   • Best for: High-performance similarity search")
    print("   • Features: Fast, memory-efficient, CPU/GPU support")
    
    # Chroma for persistence and metadata
    chroma_config = RAGConfig()
    chroma_config.vector_store.backend = "chroma"
    chroma_config.vector_store.persist_directory = "./vector_store_chroma"
    chroma_config.vector_store.collection_name = "rag_documents"
    
    print("\n2. Chroma Backend:")
    print(f"   • Backend: {chroma_config.vector_store.backend}")
    print(f"   • Collection: {chroma_config.vector_store.collection_name}")
    print("   • Best for: Persistent storage, rich metadata")
    print("   • Features: SQL-like queries, automatic persistence")
    
    # Save vector store configurations
    faiss_config.save_to_file("config_faiss.yaml")
    chroma_config.save_to_file("config_chroma.yaml")
    
    print("\n💾 Vector store configurations saved:")
    print("   • config_faiss.yaml - FAISS backend")
    print("   • config_chroma.yaml - Chroma backend")
    
    print("\n🔄 Migration between backends:")
    print("   1. Export documents from current backend")
    print("   2. Change backend in configuration")
    print("   3. Re-load documents into new backend")


def main():
    """Run all custom model examples."""
    example_with_different_models()
    example_with_custom_chunking()
    example_with_vector_stores()
    
    print("\n\n🎯 Summary")
    print("=" * 40)
    print("✅ Created multiple configuration examples")
    print("📝 All configurations saved as YAML files")
    print("🚀 Ready to use with the RAG system")
    
    print("\n💡 Tips:")
    print("   • Start with CPU config for testing")
    print("   • Use GPU config for production")
    print("   • Adjust chunk size based on your documents")
    print("   • Choose vector store based on your needs")
    
    print("\n🔗 Next steps:")
    print("   python -m rag_system -c <config_file> test")
    print("   python -m rag_system -c <config_file> load -s ./docs")
    print("   python -m rag_system -c <config_file> query --interactive")


if __name__ == "__main__":
    main()
