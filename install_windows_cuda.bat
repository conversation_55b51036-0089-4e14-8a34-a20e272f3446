@echo off
echo 🚀 Installing RAG System with CUDA Support on Windows
echo ============================================================

REM Check if virtual environment exists
if not exist ".venv" (
    echo 📦 Creating virtual environment...
    python -m venv .venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
)

echo 🔄 Activating virtual environment...
call .venv\Scripts\activate.bat

echo 📋 Upgrading pip...
python -m pip install --upgrade pip

echo 🗑️ Uninstalling existing PyTorch (if any)...
pip uninstall torch torchvision torchaudio -y

echo 🔥 Installing PyTorch with CUDA 11.8...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

if errorlevel 1 (
    echo ❌ Failed to install PyTorch with CUDA
    echo 💡 Falling back to CPU version...
    pip install torch torchvision torchaudio
)

echo 📦 Installing other dependencies...
pip install -r requirements.txt

echo 🔍 Testing CUDA availability...
python -c "import torch; print('CUDA available:', torch.cuda.is_available()); print('Device count:', torch.cuda.device_count()) if torch.cuda.is_available() else None"

echo ✅ Installation complete!
echo.
echo 🚀 Next steps:
echo    1. Test the system: python -m rag_system test
echo    2. Run basic example: python examples/basic_usage.py
echo    3. Start web interface: python examples/web_demo.py
echo.
pause
