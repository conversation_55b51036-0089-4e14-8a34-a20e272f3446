#!/usr/bin/env python3
"""
Basic usage example for the RAG system.
This script demonstrates how to use the RAG system programmatically.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import rag_system
sys.path.insert(0, str(Path(__file__).parent.parent))

from rag_system import RAGConfig, RAGPipeline


def main():
    """Main example function."""
    print("🤖 RAG System Basic Usage Example")
    print("=" * 50)
    
    # 1. Create configuration
    print("\n1. Setting up configuration...")
    config = RAGConfig()
    
    # Customize configuration for this example
    config.embedding.model_name = "thenlper/gte-small"
    config.llm.model_name = "microsoft/DialoGPT-medium"
    config.vector_store.backend = "faiss"
    config.reranker.enabled = True
    
    print(f"   • Embedding model: {config.embedding.model_name}")
    print(f"   • LLM model: {config.llm.model_name}")
    print(f"   • Vector store: {config.vector_store.backend}")
    print(f"   • Reranker enabled: {config.reranker.enabled}")
    
    # 2. Initialize pipeline
    print("\n2. Initializing RAG pipeline...")
    pipeline = RAGPipeline(config)
    
    try:
        pipeline.initialize()
        print("   ✅ Pipeline initialized successfully!")
    except Exception as e:
        print(f"   ❌ Failed to initialize pipeline: {e}")
        return
    
    # 3. Create sample documents
    print("\n3. Creating sample documents...")
    sample_docs_dir = Path("sample_docs")
    sample_docs_dir.mkdir(exist_ok=True)
    
    # Create sample documents
    sample_texts = {
        "machine_learning.txt": """
Machine Learning is a subset of artificial intelligence (AI) that focuses on the development of algorithms and statistical models that enable computer systems to improve their performance on a specific task through experience, without being explicitly programmed.

Key concepts in machine learning include:
- Supervised learning: Learning from labeled training data
- Unsupervised learning: Finding patterns in data without labels
- Reinforcement learning: Learning through interaction with an environment
- Deep learning: Using neural networks with multiple layers

Common applications include image recognition, natural language processing, recommendation systems, and autonomous vehicles.
        """,
        
        "deep_learning.txt": """
Deep Learning is a subset of machine learning that uses artificial neural networks with multiple layers (hence "deep") to model and understand complex patterns in data.

Key characteristics of deep learning:
- Multiple hidden layers in neural networks
- Automatic feature extraction from raw data
- Requires large amounts of training data
- Computationally intensive, often requires GPUs

Popular deep learning architectures:
- Convolutional Neural Networks (CNNs) for image processing
- Recurrent Neural Networks (RNNs) for sequential data
- Transformers for natural language processing
- Generative Adversarial Networks (GANs) for data generation

Deep learning has revolutionized fields like computer vision, natural language processing, and speech recognition.
        """,
        
        "ai_overview.txt": """
Artificial Intelligence (AI) is a broad field of computer science focused on creating systems that can perform tasks that typically require human intelligence.

AI can be categorized into:
- Narrow AI: Systems designed for specific tasks (current state)
- General AI: Systems with human-like cognitive abilities (future goal)
- Superintelligence: AI that surpasses human intelligence (theoretical)

Major AI techniques include:
- Machine learning and deep learning
- Natural language processing
- Computer vision
- Robotics
- Expert systems
- Planning and optimization

AI applications are everywhere: search engines, recommendation systems, virtual assistants, autonomous vehicles, medical diagnosis, and financial trading.
        """
    }
    
    # Write sample documents
    for filename, content in sample_texts.items():
        doc_path = sample_docs_dir / filename
        with open(doc_path, 'w', encoding='utf-8') as f:
            f.write(content.strip())
    
    print(f"   ✅ Created {len(sample_texts)} sample documents in {sample_docs_dir}")
    
    # 4. Load documents
    print("\n4. Loading documents into the pipeline...")
    try:
        doc_count = pipeline.load_documents(str(sample_docs_dir), "directory")
        print(f"   ✅ Loaded {doc_count} document chunks")
    except Exception as e:
        print(f"   ❌ Failed to load documents: {e}")
        return
    
    # 5. Test queries
    print("\n5. Testing queries...")
    test_questions = [
        "What is machine learning?",
        "How does deep learning differ from traditional machine learning?",
        "What are the main types of AI?",
        "What are some applications of artificial intelligence?"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n   Question {i}: {question}")
        try:
            result = pipeline.query(question)
            
            print(f"   Answer: {result['answer'][:200]}...")
            print(f"   Sources: {result['final_count']} documents used")
            print(f"   Reranked: {'Yes' if result['reranked'] else 'No'}")
            
        except Exception as e:
            print(f"   ❌ Query failed: {e}")
    
    # 6. Save pipeline
    print("\n6. Saving pipeline...")
    try:
        pipeline.save_pipeline()
        print("   ✅ Pipeline saved successfully!")
        print(f"   📁 Saved to: {config.vector_store.persist_directory}")
    except Exception as e:
        print(f"   ❌ Failed to save pipeline: {e}")
    
    # 7. Get pipeline statistics
    print("\n7. Pipeline statistics:")
    stats = pipeline.get_pipeline_stats()
    print(f"   • Documents loaded: {stats['documents_loaded']}")
    print(f"   • Vector store backend: {stats['config']['vector_store_backend']}")
    if 'vector_store_stats' in stats:
        print(f"   • Document count: {stats['vector_store_stats']['document_count']}")
        print(f"   • Embedding dimension: {stats['vector_store_stats']['embedding_dimension']}")
    
    print("\n🎉 Example completed successfully!")
    print("\nNext steps:")
    print("- Try the CLI: python -m rag_system query --interactive")
    print("- Start web UI: python -m rag_system.web_ui")
    print("- Load your own documents: python -m rag_system load -s /path/to/docs")


if __name__ == "__main__":
    main()
