#!/usr/bin/env python3
"""
Quick start script for the RAG system.
This script provides an easy way to get started with the RAG system.
"""

import sys
import subprocess
from pathlib import Path
import argparse


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        return False


def setup_environment():
    """Set up the environment."""
    print("🚀 Setting up RAG System Environment")
    print("=" * 50)
    
    # Check if virtual environment exists
    venv_path = Path(".venv")
    if not venv_path.exists():
        print("📦 Creating virtual environment...")
        if not run_command("python -m venv .venv", "Virtual environment creation"):
            return False
    
    # Determine activation command
    if sys.platform == "win32":
        activate_cmd = ".venv\\Scripts\\activate"
        pip_cmd = ".venv\\Scripts\\pip"
    else:
        activate_cmd = "source .venv/bin/activate"
        pip_cmd = ".venv/bin/pip"
    
    print(f"\n📝 To activate the virtual environment manually:")
    print(f"   {activate_cmd}")
    
    # Install dependencies
    if Path("requirements.txt").exists():
        print("\n📦 Installing dependencies...")
        install_cmd = f"{pip_cmd} install -r requirements.txt"
        if not run_command(install_cmd, "Dependencies installation"):
            print("⚠️  You may need to activate the virtual environment and install manually:")
            print(f"   {activate_cmd}")
            print(f"   pip install -r requirements.txt")
            return False
    
    return True


def quick_demo():
    """Run a quick demo."""
    print("\n🎯 Running Quick Demo")
    print("=" * 50)
    
    # Run basic usage example
    if Path("examples/basic_usage.py").exists():
        print("📚 Running basic usage example...")
        if run_command("python examples/basic_usage.py", "Basic usage demo"):
            print("\n🎉 Demo completed! Check the output above.")
            return True
    
    print("❌ Demo files not found. Please ensure you're in the correct directory.")
    return False


def start_web_ui():
    """Start the web interface."""
    print("\n🌐 Starting Web Interface")
    print("=" * 50)
    
    print("🚀 Launching web interface...")
    print("📱 The interface will open in your browser at http://localhost:7860")
    print("🛑 Press Ctrl+C to stop the server")
    
    try:
        if Path("examples/web_demo.py").exists():
            subprocess.run("python examples/web_demo.py", shell=True)
        else:
            subprocess.run("python -c \"from rag_system.web_ui import create_web_ui; create_web_ui()\"", shell=True)
    except KeyboardInterrupt:
        print("\n👋 Web interface stopped")


def run_tests():
    """Run system tests."""
    print("\n🧪 Running System Tests")
    print("=" * 50)
    
    if Path("run_tests.py").exists():
        return run_command("python run_tests.py", "System tests")
    else:
        return run_command("python -m rag_system test", "Basic system test")


def interactive_cli():
    """Start interactive CLI."""
    print("\n💬 Starting Interactive CLI")
    print("=" * 50)
    
    print("🎯 Starting interactive mode...")
    print("💡 You can ask questions about your documents")
    print("🛑 Type 'quit' or press Ctrl+C to exit")
    
    try:
        subprocess.run("python -m rag_system query --interactive", shell=True)
    except KeyboardInterrupt:
        print("\n👋 Interactive mode stopped")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Quick start script for the RAG system",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python start_rag.py setup          # Set up environment
  python start_rag.py demo           # Run quick demo
  python start_rag.py web            # Start web interface
  python start_rag.py test           # Run tests
  python start_rag.py chat           # Start interactive CLI
        """
    )
    
    parser.add_argument(
        "action",
        choices=["setup", "demo", "web", "test", "chat"],
        help="Action to perform"
    )
    
    args = parser.parse_args()
    
    print("🤖 Advanced RAG System - Quick Start")
    print("=" * 60)
    
    if args.action == "setup":
        success = setup_environment()
        if success:
            print("\n🎉 Setup completed successfully!")
            print("\n🚀 Next steps:")
            print("   python start_rag.py demo    # Run a quick demo")
            print("   python start_rag.py web     # Start web interface")
            print("   python start_rag.py test    # Run system tests")
        else:
            print("\n❌ Setup failed. Please check the errors above.")
            sys.exit(1)
    
    elif args.action == "demo":
        success = quick_demo()
        if success:
            print("\n🚀 Next steps:")
            print("   python start_rag.py web     # Start web interface")
            print("   python start_rag.py chat    # Interactive CLI")
        else:
            print("\n❌ Demo failed. Try running setup first:")
            print("   python start_rag.py setup")
            sys.exit(1)
    
    elif args.action == "web":
        start_web_ui()
    
    elif args.action == "test":
        success = run_tests()
        if not success:
            print("\n❌ Tests failed. Please check the errors above.")
            sys.exit(1)
    
    elif args.action == "chat":
        interactive_cli()


if __name__ == "__main__":
    main()
