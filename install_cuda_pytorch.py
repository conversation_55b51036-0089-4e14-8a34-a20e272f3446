#!/usr/bin/env python3
"""
Script to install PyTorch with CUDA 11.8 support.
This script will uninstall CPU-only PyTorch and install CUDA-enabled version.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    print(f"Command: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout.strip():
            print(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e.stderr}")
        return False

def check_cuda():
    """Check CUDA availability."""
    try:
        result = subprocess.run([
            sys.executable, "-c", 
            "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); "
            "print(f'CUDA version: {torch.version.cuda}') if torch.cuda.is_available() else None; "
            "print(f'Device count: {torch.cuda.device_count()}') if torch.cuda.is_available() else None"
        ], capture_output=True, text=True, check=True)
        
        print("🔍 Current PyTorch CUDA status:")
        print(result.stdout)
        
        return "CUDA available: True" in result.stdout
    except Exception as e:
        print(f"❌ Error checking CUDA: {e}")
        return False

def install_cuda_pytorch():
    """Install PyTorch with CUDA 11.8 support."""
    print("🚀 Installing PyTorch with CUDA 11.8 Support")
    print("=" * 60)
    
    # Check current status
    print("📋 Checking current PyTorch installation...")
    has_cuda = check_cuda()
    
    if has_cuda:
        print("✅ PyTorch with CUDA is already installed!")
        return True
    
    print("⚠️  PyTorch without CUDA detected. Installing CUDA version...")
    
    # Determine pip command
    if os.name == 'nt':  # Windows
        pip_cmd = ".venv\\Scripts\\pip" if Path(".venv").exists() else "pip"
    else:  # Unix/Linux/MacOS
        pip_cmd = ".venv/bin/pip" if Path(".venv").exists() else "pip"
    
    # Step 1: Uninstall existing PyTorch
    print("\n🗑️  Uninstalling existing PyTorch packages...")
    uninstall_packages = [
        "torch", "torchvision", "torchaudio", 
        "faiss-cpu"  # Also remove CPU-only FAISS
    ]
    
    for package in uninstall_packages:
        uninstall_cmd = f"{pip_cmd} uninstall {package} -y"
        run_command(uninstall_cmd, f"Uninstalling {package}")
    
    # Step 2: Install PyTorch with CUDA 11.8
    print("\n🔥 Installing PyTorch with CUDA 11.8...")
    pytorch_install_cmd = (
        f"{pip_cmd} install torch torchvision torchaudio "
        f"--index-url https://download.pytorch.org/whl/cu118"
    )
    
    if not run_command(pytorch_install_cmd, "Installing PyTorch with CUDA 11.8"):
        print("❌ Failed to install PyTorch with CUDA")
        return False
    
    # Step 3: Install FAISS with GPU support
    print("\n🗄️  Installing FAISS with GPU support...")
    faiss_install_cmd = f"{pip_cmd} install faiss-gpu"
    
    if not run_command(faiss_install_cmd, "Installing FAISS-GPU"):
        print("⚠️  Failed to install FAISS-GPU, falling back to CPU version")
        fallback_cmd = f"{pip_cmd} install faiss-cpu"
        run_command(fallback_cmd, "Installing FAISS-CPU as fallback")
    
    # Step 4: Verify installation
    print("\n🔍 Verifying CUDA installation...")
    has_cuda_after = check_cuda()
    
    if has_cuda_after:
        print("🎉 Successfully installed PyTorch with CUDA support!")
        
        # Additional verification
        try:
            result = subprocess.run([
                sys.executable, "-c",
                "import torch; "
                "print(f'PyTorch version: {torch.__version__}'); "
                "print(f'CUDA version: {torch.version.cuda}'); "
                "print(f'cuDNN version: {torch.backends.cudnn.version()}'); "
                "print(f'GPU devices: {[torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())]}');"
            ], capture_output=True, text=True, check=True)
            
            print("\n📊 Installation Details:")
            print(result.stdout)
            
        except Exception as e:
            print(f"⚠️  Could not get detailed info: {e}")
        
        return True
    else:
        print("❌ CUDA installation verification failed")
        print("\n🔧 Troubleshooting steps:")
        print("1. Check if your GPU supports CUDA 11.8")
        print("2. Verify NVIDIA drivers are installed and up to date")
        print("3. Check CUDA toolkit installation")
        print("4. Try restarting your terminal/IDE")
        return False

def main():
    """Main function."""
    print("🔥 CUDA PyTorch Installation Script")
    print("=" * 60)
    print("This script will install PyTorch with CUDA 11.8 support")
    print("for your RAG system to use GPU acceleration.")
    print("=" * 60)
    
    # Check if we're in a virtual environment
    if Path(".venv").exists():
        print("📦 Virtual environment detected")
        if os.name == 'nt':
            print("💡 Make sure to activate it: .venv\\Scripts\\activate")
        else:
            print("💡 Make sure to activate it: source .venv/bin/activate")
    else:
        print("⚠️  No virtual environment detected")
        print("💡 Consider creating one: python -m venv .venv")
    
    # Ask for confirmation
    response = input("\n❓ Do you want to proceed with CUDA PyTorch installation? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("❌ Installation cancelled")
        return
    
    # Install CUDA PyTorch
    success = install_cuda_pytorch()
    
    if success:
        print("\n🎉 Installation completed successfully!")
        print("\n🚀 Next steps:")
        print("1. Test CUDA: python -c \"import torch; print('CUDA:', torch.cuda.is_available())\"")
        print("2. Run RAG system test: python -m rag_system test")
        print("3. Start using the RAG system with GPU acceleration!")
    else:
        print("\n❌ Installation failed")
        print("You can still use the RAG system with CPU-only mode.")
        print("Check the troubleshooting steps above.")

if __name__ == "__main__":
    main()
