"""
Document retriever with advanced search capabilities.
"""

import logging
from typing import List, Optional, Dict, Any
from langchain.docstore.document import Document as LangchainDocument
from ..config import RetrieverConfig
from .vector_store import VectorStore

logger = logging.getLogger(__name__)


class Retriever:
    """
    Advanced document retriever with multiple search strategies.
    """
    
    def __init__(self, config: RetrieverConfig, vector_store: VectorStore):
        """
        Initialize the retriever.
        
        Args:
            config: Retriever configuration
            vector_store: Vector store for document retrieval
        """
        self.config = config
        self.vector_store = vector_store
        
        logger.info(f"Initialized retriever with search type: {config.search_type}")
        logger.info(f"Default top_k: {config.top_k}")
        if config.score_threshold:
            logger.info(f"Score threshold: {config.score_threshold}")
    
    def retrieve(self, query: str, top_k: Optional[int] = None, 
                search_type: Optional[str] = None,
                score_threshold: Optional[float] = None) -> List[LangchainDocument]:
        """
        Retrieve relevant documents for a query.
        
        Args:
            query: Search query
            top_k: Number of documents to retrieve (if None, use config default)
            search_type: Search strategy (if None, use config default)
            score_threshold: Minimum similarity score (if None, use config default)
            
        Returns:
            List of relevant documents
        """
        if not query.strip():
            raise ValueError("Query cannot be empty")
        
        # Use provided parameters or fall back to config defaults
        k = top_k if top_k is not None else self.config.top_k
        search_strategy = search_type if search_type is not None else self.config.search_type
        threshold = score_threshold if score_threshold is not None else self.config.score_threshold
        
        logger.debug(f"Retrieving documents for query: {query[:100]}...")
        logger.debug(f"Parameters - top_k: {k}, search_type: {search_strategy}, threshold: {threshold}")
        
        try:
            if search_strategy == "similarity":
                documents = self._similarity_search(query, k, threshold)
            elif search_strategy == "mmr":
                documents = self._mmr_search(query, k, threshold)
            else:
                raise ValueError(f"Unsupported search type: {search_strategy}")
            
            logger.info(f"Retrieved {len(documents)} documents")
            return documents
            
        except Exception as e:
            logger.error(f"Error during document retrieval: {e}")
            raise
    
    def retrieve_with_scores(self, query: str, top_k: Optional[int] = None) -> List[tuple]:
        """
        Retrieve documents with similarity scores.
        
        Args:
            query: Search query
            top_k: Number of documents to retrieve
            
        Returns:
            List of (document, score) tuples
        """
        if not query.strip():
            raise ValueError("Query cannot be empty")
        
        k = top_k if top_k is not None else self.config.top_k
        
        try:
            docs_with_scores = self.vector_store.similarity_search_with_score(query, k=k)
            
            # Apply score threshold if configured
            if self.config.score_threshold:
                docs_with_scores = [
                    (doc, score) for doc, score in docs_with_scores 
                    if score >= self.config.score_threshold
                ]
            
            logger.info(f"Retrieved {len(docs_with_scores)} documents with scores")
            return docs_with_scores
            
        except Exception as e:
            logger.error(f"Error during document retrieval with scores: {e}")
            raise
    
    def _similarity_search(self, query: str, k: int, 
                          score_threshold: Optional[float]) -> List[LangchainDocument]:
        """Perform similarity search."""
        return self.vector_store.similarity_search(
            query=query, 
            k=k, 
            score_threshold=score_threshold
        )
    
    def _mmr_search(self, query: str, k: int, 
                   score_threshold: Optional[float]) -> List[LangchainDocument]:
        """
        Perform Maximum Marginal Relevance (MMR) search.
        MMR balances relevance and diversity in results.
        """
        try:
            # Check if the vector store supports MMR
            if hasattr(self.vector_store.store, 'max_marginal_relevance_search'):
                documents = self.vector_store.store.max_marginal_relevance_search(
                    query=query,
                    k=k,
                    fetch_k=k * 2,  # Fetch more candidates for diversity
                    lambda_mult=0.5  # Balance between relevance and diversity
                )
                
                # Apply score threshold if needed (MMR doesn't return scores directly)
                if score_threshold:
                    # Fall back to similarity search with threshold
                    logger.warning("Score threshold not supported with MMR, falling back to similarity search")
                    return self._similarity_search(query, k, score_threshold)
                
                return documents
            else:
                logger.warning("MMR not supported by vector store, falling back to similarity search")
                return self._similarity_search(query, k, score_threshold)
                
        except Exception as e:
            logger.error(f"Error in MMR search: {e}")
            # Fall back to similarity search
            logger.info("Falling back to similarity search")
            return self._similarity_search(query, k, score_threshold)
    
    def batch_retrieve(self, queries: List[str], top_k: Optional[int] = None) -> List[List[LangchainDocument]]:
        """
        Retrieve documents for multiple queries.
        
        Args:
            queries: List of search queries
            top_k: Number of documents to retrieve per query
            
        Returns:
            List of document lists, one for each query
        """
        if not queries:
            return []
        
        results = []
        for query in queries:
            try:
                docs = self.retrieve(query, top_k=top_k)
                results.append(docs)
            except Exception as e:
                logger.error(f"Error retrieving documents for query '{query}': {e}")
                results.append([])  # Empty list for failed queries
        
        return results
    
    def get_retrieval_stats(self) -> Dict[str, Any]:
        """Get statistics about the retriever configuration."""
        return {
            'search_type': self.config.search_type,
            'top_k': self.config.top_k,
            'score_threshold': self.config.score_threshold,
            'vector_store_stats': self.vector_store.get_stats()
        }
    
    def test_retrieval(self, test_query: str = "test query") -> Dict[str, Any]:
        """
        Test the retrieval system with a sample query.
        
        Args:
            test_query: Query to test with
            
        Returns:
            Test results and statistics
        """
        try:
            # Test basic retrieval
            docs = self.retrieve(test_query, top_k=3)
            
            # Test retrieval with scores
            docs_with_scores = self.retrieve_with_scores(test_query, top_k=3)
            
            results = {
                'success': True,
                'query': test_query,
                'documents_retrieved': len(docs),
                'documents_with_scores': len(docs_with_scores),
                'sample_scores': [score for _, score in docs_with_scores[:3]],
                'retriever_stats': self.get_retrieval_stats()
            }
            
            if docs:
                results['sample_document_length'] = len(docs[0].page_content)
                results['sample_document_source'] = docs[0].metadata.get('source', 'unknown')
            
            logger.info("Retrieval test completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Retrieval test failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'query': test_query
            }
