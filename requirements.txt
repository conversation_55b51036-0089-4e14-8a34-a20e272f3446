# Core RAG Dependencies
torch>=2.0.0
transformers>=4.35.0
sentence-transformers>=2.2.2
langchain>=0.1.0
langchain-community>=0.0.10

# Vector Database - Chroma (better Windows support than FAISS)
chromadb>=0.4.15

# Optional: FAISS for high-performance scenarios
# faiss-cpu>=1.7.4

# Document Processing
pypdf>=3.17.0
python-docx>=0.8.11
openpyxl>=3.1.2
datasets>=2.14.0

# Reranking
ragatouille>=0.0.7

# Vector Database Alternatives
qdrant-client>=1.6.0

# LLM Integration
accelerate>=0.24.0
bitsandbytes>=0.41.0

# Web Interface (Optional)
gradio>=4.0.0
streamlit>=1.28.0

# Utilities
python-dotenv>=1.0.0
pydantic>=2.5.0
click>=8.1.0
tqdm>=4.66.0
numpy>=1.24.0
pandas>=2.1.0

# Development
pytest>=7.4.0
black>=23.0.0
isort>=5.12.0
flake8>=6.1.0

# Jupyter for experimentation
jupyter>=1.0.0
ipykernel>=6.25.0
