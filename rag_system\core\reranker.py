"""
Document reranker for improving retrieval quality.
"""

import logging
from typing import List, Optional, Dict, Any
from langchain.docstore.document import Document as LangchainDocument
from ..config import RerankerConfig

logger = logging.getLogger(__name__)


class Reranker:
    """
    Document reranker using ColBERT or other reranking models.
    """
    
    def __init__(self, config: RerankerConfig):
        """
        Initialize the reranker.
        
        Args:
            config: Reranker configuration
        """
        self.config = config
        self.model = None
        
        if config.enabled:
            self._load_model()
        else:
            logger.info("Reranker disabled in configuration")
    
    def _load_model(self):
        """Load the reranking model."""
        try:
            from ragatouille import RAGPretrainedModel
            
            logger.info(f"Loading reranker model: {self.config.model_name}")
            self.model = RAGPretrainedModel.from_pretrained(self.config.model_name)
            logger.info("Reranker model loaded successfully")
            
        except ImportError:
            logger.error("RAGatouille not available. Install with: pip install ragatouille")
            raise
        except Exception as e:
            logger.error(f"Failed to load reranker model: {e}")
            raise
    
    def rerank(self, query: str, documents: List[LangchainDocument], 
               top_k: Optional[int] = None) -> List[LangchainDocument]:
        """
        Rerank documents based on query relevance.
        
        Args:
            query: Search query
            documents: List of documents to rerank
            top_k: Number of top documents to return (if None, use config default)
            
        Returns:
            Reranked list of documents
        """
        if not self.config.enabled or self.model is None:
            logger.debug("Reranker disabled, returning original documents")
            return documents[:top_k] if top_k else documents
        
        if not documents:
            return []
        
        if not query.strip():
            logger.warning("Empty query provided for reranking")
            return documents[:top_k] if top_k else documents
        
        k = top_k if top_k is not None else self.config.top_k
        
        try:
            logger.debug(f"Reranking {len(documents)} documents for query: {query[:50]}...")
            
            # Extract text content from documents
            doc_texts = [doc.page_content for doc in documents]
            
            # Perform reranking
            reranked_results = self.model.rerank(
                query=query,
                documents=doc_texts,
                k=min(k, len(documents))
            )
            
            # Map results back to original documents
            reranked_docs = []
            for result in reranked_results:
                # Find the original document by content
                original_doc = None
                for doc in documents:
                    if doc.page_content == result["content"]:
                        original_doc = doc
                        break
                
                if original_doc:
                    # Add reranking score to metadata
                    reranked_doc = LangchainDocument(
                        page_content=original_doc.page_content,
                        metadata={
                            **original_doc.metadata,
                            'rerank_score': result.get('score', 0.0),
                            'rerank_rank': result.get('rank', 0)
                        }
                    )
                    reranked_docs.append(reranked_doc)
            
            logger.info(f"Reranked to {len(reranked_docs)} documents")
            return reranked_docs
            
        except Exception as e:
            logger.error(f"Error during reranking: {e}")
            logger.info("Falling back to original document order")
            return documents[:k]
    
    def rerank_with_scores(self, query: str, documents: List[LangchainDocument], 
                          top_k: Optional[int] = None) -> List[tuple]:
        """
        Rerank documents and return with scores.
        
        Args:
            query: Search query
            documents: List of documents to rerank
            top_k: Number of top documents to return
            
        Returns:
            List of (document, rerank_score) tuples
        """
        if not self.config.enabled or self.model is None:
            # Return with dummy scores if reranker is disabled
            k = top_k if top_k is not None else len(documents)
            return [(doc, 1.0) for doc in documents[:k]]
        
        reranked_docs = self.rerank(query, documents, top_k)
        
        # Extract scores from metadata
        docs_with_scores = []
        for doc in reranked_docs:
            score = doc.metadata.get('rerank_score', 0.0)
            docs_with_scores.append((doc, score))
        
        return docs_with_scores
    
    def batch_rerank(self, queries: List[str], document_lists: List[List[LangchainDocument]], 
                    top_k: Optional[int] = None) -> List[List[LangchainDocument]]:
        """
        Rerank documents for multiple queries.
        
        Args:
            queries: List of search queries
            document_lists: List of document lists, one for each query
            top_k: Number of top documents to return per query
            
        Returns:
            List of reranked document lists
        """
        if len(queries) != len(document_lists):
            raise ValueError("Number of queries must match number of document lists")
        
        results = []
        for query, docs in zip(queries, document_lists):
            try:
                reranked_docs = self.rerank(query, docs, top_k)
                results.append(reranked_docs)
            except Exception as e:
                logger.error(f"Error reranking documents for query '{query}': {e}")
                k = top_k if top_k is not None else len(docs)
                results.append(docs[:k])  # Fall back to original order
        
        return results
    
    def is_enabled(self) -> bool:
        """Check if reranker is enabled and loaded."""
        return self.config.enabled and self.model is not None
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the reranker model."""
        info = {
            'enabled': self.config.enabled,
            'model_name': self.config.model_name,
            'top_k': self.config.top_k,
            'model_loaded': self.model is not None
        }
        
        if self.model and hasattr(self.model, 'model'):
            try:
                # Try to get additional model information
                info['model_type'] = type(self.model.model).__name__
            except:
                pass
        
        return info
    
    def test_reranking(self, test_query: str = "test query", 
                      test_docs: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Test the reranking system.
        
        Args:
            test_query: Query to test with
            test_docs: List of test document texts (if None, use default)
            
        Returns:
            Test results
        """
        if test_docs is None:
            test_docs = [
                "This is a test document about machine learning.",
                "This document discusses natural language processing.",
                "A document about computer vision and image recognition.",
                "Information about deep learning and neural networks."
            ]
        
        # Convert to LangchainDocument objects
        documents = [
            LangchainDocument(
                page_content=text,
                metadata={'source': f'test_doc_{i}', 'test': True}
            )
            for i, text in enumerate(test_docs)
        ]
        
        try:
            if self.config.enabled and self.model is not None:
                reranked_docs = self.rerank(test_query, documents, top_k=3)
                
                results = {
                    'success': True,
                    'query': test_query,
                    'original_count': len(documents),
                    'reranked_count': len(reranked_docs),
                    'reranker_enabled': True,
                    'model_info': self.get_model_info()
                }
                
                # Add score information if available
                if reranked_docs and 'rerank_score' in reranked_docs[0].metadata:
                    results['rerank_scores'] = [
                        doc.metadata.get('rerank_score', 0.0) 
                        for doc in reranked_docs
                    ]
            else:
                results = {
                    'success': True,
                    'query': test_query,
                    'original_count': len(documents),
                    'reranked_count': len(documents),
                    'reranker_enabled': False,
                    'message': 'Reranker disabled or not loaded'
                }
            
            logger.info("Reranking test completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Reranking test failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'query': test_query,
                'reranker_enabled': self.config.enabled
            }
