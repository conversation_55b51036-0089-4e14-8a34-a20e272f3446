"""
Configuration management for the RAG system.
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
import yaml
import json


class EmbeddingConfig(BaseModel):
    """Configuration for embedding models."""
    model_name: str = "thenlper/gte-small"
    device: str = "auto"  # auto, cpu, cuda
    max_seq_length: int = 512
    batch_size: int = 32


class VectorStoreConfig(BaseModel):
    """Configuration for vector stores."""
    backend: str = "chroma"  # chroma, faiss, qdrant
    index_type: str = "flat"  # flat, ivf, hnsw (for faiss)
    persist_directory: Optional[str] = "./vector_store"
    collection_name: str = "rag_documents"


class TextSplitterConfig(BaseModel):
    """Configuration for text splitting."""
    chunk_size: int = 512
    chunk_overlap: int = 50
    separators: List[str] = Field(default_factory=lambda: [
        "\n#{1,6} ",
        "```\n",
        "\n\\*\\*\\*+\n",
        "\n---+\n",
        "\n___+\n",
        "\n\n",
        "\n",
        " ",
        "",
    ])


class RetrieverConfig(BaseModel):
    """Configuration for document retrieval."""
    top_k: int = 30
    search_type: str = "similarity"  # similarity, mmr
    score_threshold: Optional[float] = None


class RerankerConfig(BaseModel):
    """Configuration for document reranking."""
    enabled: bool = True
    model_name: str = "colbert-ir/colbertv2.0"
    top_k: int = 5


class LLMConfig(BaseModel):
    """Configuration for LLM."""
    model_name: str = "microsoft/DialoGPT-medium"
    device: str = "auto"
    max_length: int = 2048
    temperature: float = 0.7
    do_sample: bool = True
    use_cache: bool = True
    load_in_8bit: bool = False
    load_in_4bit: bool = False


class RAGConfig(BaseModel):
    """Main configuration for the RAG system."""
    
    # Component configurations
    embedding: EmbeddingConfig = Field(default_factory=EmbeddingConfig)
    vector_store: VectorStoreConfig = Field(default_factory=VectorStoreConfig)
    text_splitter: TextSplitterConfig = Field(default_factory=TextSplitterConfig)
    retriever: RetrieverConfig = Field(default_factory=RetrieverConfig)
    reranker: RerankerConfig = Field(default_factory=RerankerConfig)
    llm: LLMConfig = Field(default_factory=LLMConfig)
    
    # General settings
    data_directory: str = "./data"
    cache_directory: str = "./cache"
    log_level: str = "INFO"
    
    # CUDA settings
    use_cuda: bool = True
    cuda_device: Optional[int] = None
    
    @classmethod
    def from_file(cls, config_path: str) -> "RAGConfig":
        """Load configuration from a file."""
        config_path = Path(config_path)
        
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        if config_path.suffix.lower() in ['.yaml', '.yml']:
            with open(config_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
        elif config_path.suffix.lower() == '.json':
            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        else:
            raise ValueError(f"Unsupported configuration file format: {config_path.suffix}")
        
        return cls(**data)
    
    def save_to_file(self, config_path: str) -> None:
        """Save configuration to a file."""
        config_path = Path(config_path)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        data = self.model_dump()
        
        if config_path.suffix.lower() in ['.yaml', '.yml']:
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, indent=2)
        elif config_path.suffix.lower() == '.json':
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
        else:
            raise ValueError(f"Unsupported configuration file format: {config_path.suffix}")
    
    @classmethod
    def from_env(cls) -> "RAGConfig":
        """Create configuration from environment variables."""
        config = cls()
        
        # Override with environment variables if they exist
        if os.getenv("RAG_EMBEDDING_MODEL"):
            config.embedding.model_name = os.getenv("RAG_EMBEDDING_MODEL")
        
        if os.getenv("RAG_LLM_MODEL"):
            config.llm.model_name = os.getenv("RAG_LLM_MODEL")
        
        if os.getenv("RAG_VECTOR_STORE"):
            config.vector_store.backend = os.getenv("RAG_VECTOR_STORE")
        
        if os.getenv("RAG_USE_CUDA"):
            config.use_cuda = os.getenv("RAG_USE_CUDA").lower() == "true"
        
        return config
    
    def setup_directories(self) -> None:
        """Create necessary directories."""
        directories = [
            self.data_directory,
            self.cache_directory,
        ]
        
        if self.vector_store.persist_directory:
            directories.append(self.vector_store.persist_directory)
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
