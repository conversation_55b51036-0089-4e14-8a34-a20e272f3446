# Advanced RAG System Configuration
# This file contains all configuration options for the RAG system

# Embedding model configuration
embedding:
  model_name: "thenlper/gte-small"  # Sentence transformer model for embeddings
  device: "auto"  # auto, cpu, cuda
  max_seq_length: 512  # Maximum sequence length for embeddings
  batch_size: 32  # Batch size for embedding computation

# Vector store configuration
vector_store:
  backend: "faiss"  # faiss, chroma, qdrant
  index_type: "flat"  # flat, ivf, hnsw
  persist_directory: "./vector_store"  # Directory to save vector store
  collection_name: "rag_documents"  # Collection name for vector store

# Text splitting configuration
text_splitter:
  chunk_size: 512  # Size of text chunks in tokens
  chunk_overlap: 50  # Overlap between chunks
  separators:  # Hierarchical separators for splitting
    - "\n#{1,6} "  # Markdown headers
    - "```\n"      # Code blocks
    - "\n\\*\\*\\*+\n"  # Horizontal rules
    - "\n---+\n"
    - "\n___+\n"
    - "\n\n"       # Paragraph breaks
    - "\n"         # Line breaks
    - " "          # Spaces
    - ""           # Character level

# Document retrieval configuration
retriever:
  top_k: 30  # Number of documents to retrieve initially
  search_type: "similarity"  # similarity, mmr
  score_threshold: null  # Minimum similarity score (null = no threshold)

# Document reranking configuration
reranker:
  enabled: true  # Enable/disable reranking
  model_name: "colbert-ir/colbertv2.0"  # Reranking model
  top_k: 5  # Number of documents after reranking

# Language model configuration
llm:
  model_name: "microsoft/DialoGPT-medium"  # HuggingFace model name
  device: "auto"  # auto, cpu, cuda
  max_length: 2048  # Maximum generation length
  temperature: 0.7  # Sampling temperature
  do_sample: true  # Use sampling for generation
  use_cache: true  # Use model cache
  load_in_8bit: false  # Use 8-bit quantization
  load_in_4bit: false  # Use 4-bit quantization

# General settings
data_directory: "./data"  # Directory for data files
cache_directory: "./cache"  # Directory for cache files
log_level: "INFO"  # Logging level (DEBUG, INFO, WARNING, ERROR)

# CUDA settings
use_cuda: true  # Enable CUDA if available
cuda_device: null  # Specific CUDA device (null = auto)
