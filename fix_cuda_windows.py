#!/usr/bin/env python3
"""
Quick fix for CUDA on Windows.
This script will install PyTorch with CUDA support and fix the dependencies.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_pip_command(command, description):
    """Run a pip command."""
    print(f"\n🔄 {description}...")
    
    # Use the virtual environment pip if available
    if Path(".venv").exists():
        if os.name == 'nt':
            pip_cmd = ".venv\\Scripts\\pip"
        else:
            pip_cmd = ".venv/bin/pip"
    else:
        pip_cmd = "pip"
    
    full_command = f"{pip_cmd} {command}"
    print(f"Command: {full_command}")
    
    try:
        result = subprocess.run(full_command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def main():
    """Fix CUDA installation on Windows."""
    print("🔧 Quick CUDA Fix for Windows")
    print("=" * 50)
    
    print("This script will:")
    print("1. Install PyTorch with CUDA 11.8 support")
    print("2. Install all other dependencies")
    print("3. Test CUDA availability")
    
    # Step 1: Upgrade pip
    run_pip_command("install --upgrade pip", "Upgrading pip")
    
    # Step 2: Install PyTorch with CUDA
    print("\n🔥 Installing PyTorch with CUDA 11.8...")
    pytorch_success = run_pip_command(
        "install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118",
        "Installing PyTorch with CUDA 11.8"
    )
    
    if not pytorch_success:
        print("⚠️  CUDA installation failed, installing CPU version...")
        run_pip_command(
            "install torch torchvision torchaudio",
            "Installing PyTorch CPU version"
        )
    
    # Step 3: Install other dependencies
    print("\n📦 Installing other dependencies...")
    dependencies = [
        "transformers>=4.35.0",
        "sentence-transformers>=2.2.2", 
        "langchain>=0.1.0",
        "langchain-community>=0.0.10",
        "faiss-cpu>=1.7.4",
        "pypdf>=3.17.0",
        "python-docx>=0.8.11",
        "openpyxl>=3.1.2",
        "datasets>=2.14.0",
        "ragatouille>=0.0.7",
        "chromadb>=0.4.15",
        "accelerate>=0.24.0",
        "bitsandbytes>=0.41.0",
        "gradio>=4.0.0",
        "python-dotenv>=1.0.0",
        "pydantic>=2.5.0",
        "click>=8.1.0",
        "tqdm>=4.66.0",
        "numpy>=1.24.0",
        "pandas>=2.1.0",
        "pyyaml"
    ]
    
    for dep in dependencies:
        run_pip_command(f"install {dep}", f"Installing {dep.split('>=')[0]}")
    
    # Step 4: Test installation
    print("\n🔍 Testing installation...")
    try:
        result = subprocess.run([
            sys.executable, "-c",
            "import torch; "
            "print('✅ PyTorch version:', torch.__version__); "
            "print('🔥 CUDA available:', torch.cuda.is_available()); "
            "if torch.cuda.is_available(): "
            "    print('🎯 Device count:', torch.cuda.device_count()); "
            "    print('🖥️  Device name:', torch.cuda.get_device_name(0)); "
            "else: "
            "    print('💻 Using CPU mode');"
        ], capture_output=True, text=True, check=True)
        
        print(result.stdout)
        
        # Test other imports
        test_imports = [
            ("transformers", "Transformers"),
            ("sentence_transformers", "Sentence Transformers"),
            ("langchain", "LangChain"),
            ("faiss", "FAISS"),
            ("gradio", "Gradio")
        ]
        
        print("\n📋 Testing imports...")
        for module, name in test_imports:
            try:
                __import__(module)
                print(f"✅ {name}")
            except ImportError:
                print(f"❌ {name}")
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")
    
    print("\n🎉 Installation process completed!")
    print("\n🚀 Next steps:")
    print("1. Test the RAG system: python -m rag_system test")
    print("2. Run basic example: python examples/basic_usage.py")
    print("3. Check CUDA: python check_cuda.py")
    
    if pytorch_success:
        print("\n💡 If CUDA is still not available:")
        print("- Check your NVIDIA drivers")
        print("- Verify CUDA 11.8 is installed")
        print("- Restart your terminal/IDE")

if __name__ == "__main__":
    main()
