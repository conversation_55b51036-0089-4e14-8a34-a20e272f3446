#!/usr/bin/env python3
"""
Quick CUDA verification script.
"""

def check_cuda_detailed():
    """Perform detailed CUDA checks."""
    print("🔍 CUDA System Check")
    print("=" * 50)
    
    # Check PyTorch
    try:
        import torch
        print(f"✅ PyTorch version: {torch.__version__}")
        print(f"🔥 CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"📊 CUDA version: {torch.version.cuda}")
            print(f"🎯 Device count: {torch.cuda.device_count()}")
            print(f"📱 Current device: {torch.cuda.current_device()}")
            
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                print(f"🖥️  GPU {i}: {props.name}")
                print(f"   Memory: {props.total_memory / 1024**3:.1f} GB")
                print(f"   Compute capability: {props.major}.{props.minor}")
            
            # Test tensor operations
            try:
                x = torch.randn(3, 3).cuda()
                y = torch.randn(3, 3).cuda()
                z = torch.matmul(x, y)
                print("✅ GPU tensor operations working")
            except Exception as e:
                print(f"❌ GPU tensor operations failed: {e}")
        else:
            print("❌ CUDA not available")
            print("💡 Possible reasons:")
            print("   - PyTorch CPU-only version installed")
            print("   - NVIDIA drivers not installed")
            print("   - CUDA toolkit not installed")
            print("   - GPU not CUDA-compatible")
            
    except ImportError:
        print("❌ PyTorch not installed")
        return False
    
    # Check other GPU libraries
    print("\n🔧 Other GPU Libraries:")
    
    # Check FAISS
    try:
        import faiss
        print(f"✅ FAISS version: {faiss.__version__}")
        
        # Check if GPU FAISS is available
        try:
            res = faiss.StandardGpuResources()
            print("✅ FAISS GPU support available")
        except:
            print("⚠️  FAISS GPU support not available (CPU version)")
    except ImportError:
        print("❌ FAISS not installed")
    
    # Check transformers
    try:
        import transformers
        print(f"✅ Transformers version: {transformers.__version__}")
    except ImportError:
        print("❌ Transformers not installed")
    
    # Check sentence-transformers
    try:
        import sentence_transformers
        print(f"✅ Sentence Transformers version: {sentence_transformers.__version__}")
    except ImportError:
        print("❌ Sentence Transformers not installed")
    
    return torch.cuda.is_available() if 'torch' in locals() else False

def main():
    """Main function."""
    has_cuda = check_cuda_detailed()
    
    print("\n" + "=" * 50)
    if has_cuda:
        print("🎉 CUDA is properly configured!")
        print("🚀 Your RAG system can use GPU acceleration")
    else:
        print("⚠️  CUDA is not available")
        print("🔧 To fix this, run: python install_cuda_pytorch.py")
        print("💻 You can still use the RAG system with CPU")

if __name__ == "__main__":
    main()
