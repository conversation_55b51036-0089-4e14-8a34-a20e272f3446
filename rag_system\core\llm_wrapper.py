"""
LLM wrapper with CUDA support and optimization.
"""

import logging
import torch
from typing import List, Optional, Dict, Any, Union
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    pipeline,
    Pipeline,
    BitsAndBytesConfig
)
from ..config import LLMConfig

logger = logging.getLogger(__name__)


class LLMWrapper:
    """
    Wrapper for Large Language Models with CUDA optimization.
    """
    
    def __init__(self, config: LLMConfig):
        """
        Initialize the LLM wrapper.
        
        Args:
            config: LLM configuration
        """
        self.config = config
        self.device = self._determine_device()
        self.tokenizer = None
        self.model = None
        self.pipeline = None
        
        self._load_model()
    
    def _determine_device(self) -> str:
        """Determine the best device to use."""
        if self.config.device == "auto":
            if torch.cuda.is_available():
                device = "cuda"
                logger.info(f"CUDA available with {torch.cuda.device_count()} GPU(s)")
                for i in range(torch.cuda.device_count()):
                    logger.info(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
            else:
                device = "cpu"
                logger.info("CUDA not available, using CPU")
        else:
            device = self.config.device
        
        return device
    
    def _load_model(self):
        """Load the tokenizer and model."""
        logger.info(f"Loading LLM: {self.config.model_name}")
        logger.info(f"Using device: {self.device}")
        
        try:
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config.model_name,
                trust_remote_code=True
            )
            
            # Set pad token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            logger.info("Tokenizer loaded successfully")
            
            # Configure quantization if requested
            model_kwargs = {
                "trust_remote_code": True,
                "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32,
            }
            
            if self.config.load_in_8bit or self.config.load_in_4bit:
                if not torch.cuda.is_available():
                    logger.warning("Quantization requires CUDA, falling back to full precision")
                else:
                    quantization_config = BitsAndBytesConfig(
                        load_in_8bit=self.config.load_in_8bit,
                        load_in_4bit=self.config.load_in_4bit,
                        bnb_4bit_compute_dtype=torch.float16,
                        bnb_4bit_use_double_quant=True,
                        bnb_4bit_quant_type="nf4"
                    )
                    model_kwargs["quantization_config"] = quantization_config
                    logger.info(f"Using quantization: 8bit={self.config.load_in_8bit}, 4bit={self.config.load_in_4bit}")
            
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                self.config.model_name,
                **model_kwargs
            )
            
            # Move to device if not using quantization
            if not (self.config.load_in_8bit or self.config.load_in_4bit):
                self.model = self.model.to(self.device)
            
            # Create pipeline
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if self.device == "cuda" else -1,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                trust_remote_code=True
            )
            
            logger.info("LLM loaded successfully")
            logger.info(f"Model parameters: ~{self.model.num_parameters() / 1e6:.1f}M")
            
        except Exception as e:
            logger.error(f"Failed to load LLM: {e}")
            raise
    
    def generate(self, prompt: str, max_length: Optional[int] = None, 
                temperature: Optional[float] = None, 
                do_sample: Optional[bool] = None,
                **kwargs) -> str:
        """
        Generate text from a prompt.
        
        Args:
            prompt: Input prompt
            max_length: Maximum length of generated text
            temperature: Sampling temperature
            do_sample: Whether to use sampling
            **kwargs: Additional generation parameters
            
        Returns:
            Generated text
        """
        if self.pipeline is None:
            raise ValueError("Model not loaded")
        
        if not prompt.strip():
            raise ValueError("Prompt cannot be empty")
        
        # Use config defaults if not specified
        generation_config = {
            "max_length": max_length or self.config.max_length,
            "temperature": temperature if temperature is not None else self.config.temperature,
            "do_sample": do_sample if do_sample is not None else self.config.do_sample,
            "use_cache": self.config.use_cache,
            "pad_token_id": self.tokenizer.eos_token_id,
            "return_full_text": False,  # Only return generated text
            **kwargs
        }
        
        try:
            logger.debug(f"Generating text for prompt: {prompt[:100]}...")
            
            # Generate text
            result = self.pipeline(prompt, **generation_config)
            
            if isinstance(result, list) and len(result) > 0:
                generated_text = result[0]["generated_text"]
            else:
                generated_text = str(result)
            
            logger.debug(f"Generated {len(generated_text)} characters")
            return generated_text.strip()
            
        except Exception as e:
            logger.error(f"Error during text generation: {e}")
            raise
    
    def batch_generate(self, prompts: List[str], **kwargs) -> List[str]:
        """
        Generate text for multiple prompts.
        
        Args:
            prompts: List of input prompts
            **kwargs: Generation parameters
            
        Returns:
            List of generated texts
        """
        if not prompts:
            return []
        
        results = []
        for prompt in prompts:
            try:
                result = self.generate(prompt, **kwargs)
                results.append(result)
            except Exception as e:
                logger.error(f"Error generating text for prompt: {e}")
                results.append("")  # Empty string for failed generations
        
        return results
    
    def tokenize(self, text: str) -> List[int]:
        """
        Tokenize text.
        
        Args:
            text: Text to tokenize
            
        Returns:
            List of token IDs
        """
        if self.tokenizer is None:
            raise ValueError("Tokenizer not loaded")
        
        return self.tokenizer.encode(text)
    
    def decode(self, token_ids: List[int]) -> str:
        """
        Decode token IDs to text.
        
        Args:
            token_ids: List of token IDs
            
        Returns:
            Decoded text
        """
        if self.tokenizer is None:
            raise ValueError("Tokenizer not loaded")
        
        return self.tokenizer.decode(token_ids, skip_special_tokens=True)
    
    def get_token_count(self, text: str) -> int:
        """
        Get the number of tokens in text.
        
        Args:
            text: Text to count tokens for
            
        Returns:
            Number of tokens
        """
        return len(self.tokenize(text))
    
    def truncate_to_max_length(self, text: str, max_tokens: Optional[int] = None) -> str:
        """
        Truncate text to maximum token length.
        
        Args:
            text: Text to truncate
            max_tokens: Maximum number of tokens (if None, use model max_length)
            
        Returns:
            Truncated text
        """
        if max_tokens is None:
            max_tokens = self.config.max_length
        
        tokens = self.tokenize(text)
        if len(tokens) <= max_tokens:
            return text
        
        # Truncate tokens and decode back to text
        truncated_tokens = tokens[:max_tokens]
        return self.decode(truncated_tokens)
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        info = {
            "model_name": self.config.model_name,
            "device": self.device,
            "max_length": self.config.max_length,
            "temperature": self.config.temperature,
            "do_sample": self.config.do_sample,
            "use_cache": self.config.use_cache,
            "load_in_8bit": self.config.load_in_8bit,
            "load_in_4bit": self.config.load_in_4bit,
            "model_loaded": self.model is not None,
            "tokenizer_loaded": self.tokenizer is not None
        }
        
        if self.model:
            try:
                info["num_parameters"] = self.model.num_parameters()
                info["model_type"] = type(self.model).__name__
            except:
                pass
        
        if self.tokenizer:
            try:
                info["vocab_size"] = self.tokenizer.vocab_size
                info["model_max_length"] = getattr(self.tokenizer, 'model_max_length', 'unknown')
            except:
                pass
        
        # Add CUDA info if available
        if self.device == "cuda" and torch.cuda.is_available():
            info["cuda_device_count"] = torch.cuda.device_count()
            info["cuda_current_device"] = torch.cuda.current_device()
            info["cuda_device_name"] = torch.cuda.get_device_name()
            info["cuda_memory_allocated"] = torch.cuda.memory_allocated()
            info["cuda_memory_reserved"] = torch.cuda.memory_reserved()
        
        return info
    
    def test_generation(self, test_prompt: str = "Hello, how are you?") -> Dict[str, Any]:
        """
        Test the text generation system.
        
        Args:
            test_prompt: Prompt to test with
            
        Returns:
            Test results
        """
        try:
            generated_text = self.generate(test_prompt, max_length=50)
            
            results = {
                'success': True,
                'prompt': test_prompt,
                'generated_text': generated_text,
                'prompt_tokens': self.get_token_count(test_prompt),
                'generated_tokens': self.get_token_count(generated_text),
                'model_info': self.get_model_info()
            }
            
            logger.info("LLM generation test completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"LLM generation test failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'prompt': test_prompt
            }
