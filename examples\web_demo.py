#!/usr/bin/env python3
"""
Web demo launcher for the RAG system.
This script sets up and launches the web interface with sample data.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import rag_system
sys.path.insert(0, str(Path(__file__).parent.parent))

from rag_system.web_ui import create_web_ui
from rag_system import RAGConfig


def main():
    """Launch web demo with sample configuration."""
    print("🌐 Starting RAG System Web Demo")
    print("=" * 40)
    
    # Create a demo configuration
    config = RAGConfig()
    
    # Use smaller models for demo
    config.embedding.model_name = "thenlper/gte-small"
    config.llm.model_name = "microsoft/DialoGPT-medium"
    config.vector_store.backend = "faiss"
    config.reranker.enabled = True
    
    # Save demo configuration
    demo_config_path = "demo_config.yaml"
    config.save_to_file(demo_config_path)
    
    print(f"📝 Created demo configuration: {demo_config_path}")
    print(f"🤖 Embedding model: {config.embedding.model_name}")
    print(f"🧠 LLM model: {config.llm.model_name}")
    print(f"🗄️ Vector store: {config.vector_store.backend}")
    
    print("\n🚀 Launching web interface...")
    print("📱 The interface will open in your browser")
    print("🔗 URL: http://localhost:7860")
    print("\n💡 Demo features:")
    print("   • Upload documents in the 'Document Management' tab")
    print("   • Ask questions in the 'Chat' tab")
    print("   • View configuration in the 'Settings' tab")
    print("\n⚠️  Note: First-time model loading may take a few minutes")
    print("🛑 Press Ctrl+C to stop the server")
    
    try:
        # Launch web UI
        create_web_ui(
            config_path=demo_config_path,
            share=False,  # Set to True to create public link
            server_name="0.0.0.0",
            server_port=7860,
            show_error=True,
            quiet=False
        )
    except KeyboardInterrupt:
        print("\n\n👋 Demo stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting demo: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure all dependencies are installed: pip install -r requirements.txt")
        print("2. Check if port 7860 is available")
        print("3. Try running: python -m rag_system test")


if __name__ == "__main__":
    main()
